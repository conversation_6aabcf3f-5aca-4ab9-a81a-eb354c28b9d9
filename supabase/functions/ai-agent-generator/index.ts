import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface AIAgentRequest {
  description: string;
  userId: string;
  timestamp: string;
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Initialize Supabase client
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_ANON_KEY') ?? '',
      {
        global: {
          headers: { Authorization: req.headers.get('Authorization')! },
        },
      }
    )

    // Parse request body
    const requestData: AIAgentRequest = await req.json()
    console.log('AI Agent Generation Request:', requestData)

    // Validate request
    if (!requestData.description || !requestData.userId) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Missing required fields: description and userId'
        }),
        {
          status: 400,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )
    }

    // Get Gemini API key
    const geminiApiKey = Deno.env.get('GEMINI_API_KEY')
    if (!geminiApiKey) {
      console.error('GEMINI_API_KEY not found in environment variables')

      // Return a demo agent for testing when API key is not available
      const demoAgent = {
        name: "Demo RSI + ROE Strategy",
        description: "Demo agent: RSI below 30 AND ROE above 15% = Bullish (API key required for custom generation)",
        entryBlockId: "when-run-1",
        blocks: [
          {
            id: "when-run-1",
            type: "when_run",
            position: { x: 100, y: 100 },
            outputConnections: ["rsi-1", "roe-1"]
          },
          {
            id: "rsi-1",
            type: "indicator",
            indicator: "rsi",
            parameters: { period: 14 },
            position: { x: 400, y: 50 },
            inputConnections: ["when-run-1"],
            outputConnections: ["condition-1"]
          },
          {
            id: "roe-1",
            type: "fundamental",
            metric: "return_on_equity",
            statement: "calculated",
            period: "quarterly",
            position: { x: 400, y: 150 },
            inputConnections: ["when-run-1"],
            outputConnections: ["condition-2"]
          },
          {
            id: "condition-1",
            type: "condition",
            operator: "<",
            compareValue: 30,
            position: { x: 700, y: 50 },
            inputConnections: ["rsi-1"],
            trueConnection: "condition-2",
            falseConnection: "trigger-bearish"
          },
          {
            id: "condition-2",
            type: "condition",
            operator: ">",
            compareValue: 15,
            position: { x: 700, y: 150 },
            inputConnections: ["roe-1"],
            trueConnection: "trigger-bullish",
            falseConnection: "trigger-neutral"
          },
          {
            id: "trigger-bullish",
            type: "trigger",
            signal: "bullish",
            confidence: 85,
            position: { x: 1000, y: 50 },
            inputConnections: ["condition-2"]
          },
          {
            id: "trigger-bearish",
            type: "trigger",
            signal: "bearish",
            confidence: 70,
            position: { x: 1000, y: 100 },
            inputConnections: ["condition-1"]
          },
          {
            id: "trigger-neutral",
            type: "trigger",
            signal: "neutral",
            confidence: 60,
            position: { x: 1000, y: 150 },
            inputConnections: ["condition-2"]
          }
        ]
      }

      return new Response(
        JSON.stringify({
          success: true,
          agent: demoAgent,
          reasoning: "Demo agent generated (Gemini API key required for custom AI generation)"
        }),
        {
          status: 200,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )
    }

    // Generate agent with Gemini AI
    const aiResponse = await generateAgentWithGemini(requestData.description, geminiApiKey)

    if (!aiResponse.success) {
      return new Response(
        JSON.stringify(aiResponse),
        {
          status: 400,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )
    }

    // Log successful generation
    console.log('AI agent generated successfully:', aiResponse.agent?.name)

    return new Response(
      JSON.stringify(aiResponse),
      {
        status: 200,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    )

  } catch (error) {
    console.error('Error in ai-agent-generator:', error)
    return new Response(
      JSON.stringify({
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      }),
      {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    )
  }
})

/**
 * Generate agent configuration using Gemini AI
 */
async function generateAgentWithGemini(description: string, apiKey: string) {
  try {
    const prompt = createAgentGenerationPrompt(description)

    const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash-latest:generateContent?key=${apiKey}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        contents: [{
          parts: [{
            text: prompt
          }]
        }],
        generationConfig: {
          temperature: 0.1,
          topK: 1,
          topP: 1,
          maxOutputTokens: 4096,
        }
      })
    })

    if (!response.ok) {
      const errorText = await response.text()
      console.error('Gemini API error:', errorText)
      return {
        success: false,
        error: 'AI service temporarily unavailable'
      }
    }

    const data = await response.json()

    if (!data.candidates || !data.candidates[0] || !data.candidates[0].content) {
      console.error('Invalid Gemini response structure:', data)
      return {
        success: false,
        error: 'AI generated invalid response'
      }
    }

    const generatedText = data.candidates[0].content.parts[0].text
    console.log('Gemini generated text:', generatedText)

    // Parse the JSON response from Gemini
    const jsonMatch = generatedText.match(/```json\n([\s\S]*?)\n```/) || generatedText.match(/\`\`\`json\n([\s\S]*?)\n\`\`\`/)
    if (!jsonMatch) {
      console.error('No JSON found in Gemini response')
      console.error('Generated text:', generatedText)
      return {
        success: false,
        error: 'AI could not generate a valid agent configuration'
      }
    }

    let rawJson = jsonMatch[1]
    console.log('Raw JSON from AI:', rawJson)

    // Clean up the JSON - remove comments and fix common issues
    rawJson = rawJson
      .replace(/\/\/.*$/gm, '') // Remove single-line comments
      .replace(/\/\*[\s\S]*?\*\//g, '') // Remove multi-line comments
      .replace(/,(\s*[}\]])/g, '$1') // Remove trailing commas
      .trim()

    let agentConfig
    try {
      agentConfig = JSON.parse(rawJson)
    } catch (parseError) {
      console.error('JSON parse error:', parseError)
      console.error('Cleaned JSON:', rawJson)
      return {
        success: false,
        error: 'AI generated invalid JSON format'
      }
    }

    // Validate the generated configuration
    if (!validateGeneratedAgent(agentConfig)) {
      return {
        success: false,
        error: 'AI generated invalid agent configuration'
      }
    }

    return {
      success: true,
      agent: agentConfig,
      reasoning: extractReasoning(generatedText)
    }

  } catch (error) {
    console.error('Error generating agent with Gemini:', error)
    return {
      success: false,
      error: 'Failed to generate agent with AI'
    }
  }
}

/**
 * Create the prompt for Gemini AI to generate agent configuration
 */
function createAgentGenerationPrompt(description: string): string {
  return `You are an expert trading agent builder. Generate a complete trading agent configuration based on the user's description.

User Description: "${description}"

You must respond with a JSON configuration that includes:
1. A descriptive name for the agent
2. A brief description of what it does
3. Complete blocks array with proper connections
4. Entry block ID

Available Block Types (use these exact type names):
- when_run: Entry point (always required)
- indicator: Technical indicators (set indicator property to: rsi, sma, ema, macd, bollinger_bands, stochastic, williams_r, cci, atr, adx, support, resistance, candle_pattern)
- price: Price data (set dataPoint property to: close, high, low, open, volume)
- fundamental: Financial metrics (set metric property and statement if calculated)
- condition: Comparison operations (set operator property to: >, <, >=, <=, ==, !=)
- trigger: Final signal (set signal property to: bullish, bearish, neutral with confidence 0-100)
- operator: Math operations (set operation property to: add, subtract, multiply, divide, average)
- percentage_up: Action block for percentage gains (set percentage property to desired threshold, e.g., 5.0 for +5%)
- percentage_down: Action block for percentage losses (set percentage property to desired threshold, e.g., 10.0 for -10%)

Note: The block palette now shows one block per category. Users can configure the specific subtype (like RSI vs MACD) after placing the block.

Block Structure Requirements:
- Each block needs: id (unique), type, position {x, y}
- Connections: inputConnections[], outputConnections[]
- Conditions need: operator, compareValue, trueConnection, falseConnection (BOTH required)
- Triggers need: signal ("bullish", "bearish", or "neutral"), confidence (0-100)
- Indicators need: indicator ("rsi", "sma", "ema", "macd", "support", "resistance", "candle_pattern", etc.), parameters{period: 14} (for support/resistance use: {timeframe: "month", strength: 2} where timeframe can be "week", "month", "3month", "6month", "year"; for candle_pattern use: {timeframe: "daily", pattern: "hammer"} where timeframe can be "1min", "5min", "15min", "1hour", "daily", "weekly", "monthly" and pattern can be "any", "doji", "hammer", "shooting_star", "engulfing", "morning_star", "evening_star", "bullish", "bearish", "reversal")
- Fundamentals need: metric ("return_on_equity", "current_ratio", etc.), statement ("calculated" for ratios), period ("quarterly")
- Price blocks need: dataPoint ("close", "high", "low", "open", "volume")

Layout Guidelines:
- Start when_run at (100, 100)
- Space blocks 300px apart horizontally
- Use different Y positions for parallel paths
- Ensure proper flow from left to right

IMPORTANT: Respond with valid JSON only. No comments, no trailing commas, no extra text inside the JSON block.

Respond with this exact format:

**Reasoning:** [Explain your interpretation and strategy]

\`\`\`json
{
  "name": "RSI Oversold Strategy",
  "description": "Buys when RSI is below 30",
  "entryBlockId": "when-run-1",
  "blocks": [
    {
      "id": "when-run-1",
      "type": "when_run",
      "position": {"x": 100, "y": 100},
      "outputConnections": ["rsi-1"]
    },
    {
      "id": "rsi-1",
      "type": "indicator",
      "indicator": "rsi",
      "parameters": {"period": 14},
      "position": {"x": 400, "y": 100},
      "inputConnections": ["when-run-1"],
      "outputConnections": ["condition-1"]
    },
    {
      "id": "condition-1",
      "type": "condition",
      "operator": "<",
      "compareValue": 30,
      "position": {"x": 700, "y": 100},
      "inputConnections": ["rsi-1"],
      "trueConnection": "trigger-bullish",
      "falseConnection": "trigger-bearish"
    },
    {
      "id": "trigger-bullish",
      "type": "trigger",
      "signal": "bullish",
      "confidence": 80,
      "position": {"x": 1000, "y": 50},
      "inputConnections": ["condition-1"]
    },
    {
      "id": "trigger-bearish",
      "type": "trigger",
      "signal": "bearish",
      "confidence": 60,
      "position": {"x": 1000, "y": 150},
      "inputConnections": ["condition-1"]
    }
  ]
}
\`\`\`

Generate a complete, valid configuration now.`
}

/**
 * Validate the generated agent configuration
 */
function validateGeneratedAgent(config: any): boolean {
  if (!config.name || !config.blocks || !config.entryBlockId) {
    return false
  }

  if (!Array.isArray(config.blocks) || config.blocks.length === 0) {
    return false
  }

  // Check for required when_run and trigger blocks
  const hasWhenRun = config.blocks.some((b: any) => b.type === 'when_run')
  const hasTrigger = config.blocks.some((b: any) => b.type === 'trigger')

  return hasWhenRun && hasTrigger
}

/**
 * Extract reasoning from the generated text
 */
function extractReasoning(text: string): string {
  const reasoningMatch = text.match(/\*\*Reasoning:\*\*\s*(.*?)(?=```json|$)/s)
  return reasoningMatch ? reasoningMatch[1].trim() : ''
}
