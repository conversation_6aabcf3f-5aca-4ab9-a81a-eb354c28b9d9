// Polygon API Integration
// Functions to fetch data from Polygon API

import { AgentConfig, BlockType, PolygonData } from "./agent-types.ts";

/**
 * Fetch data from Polygon API
 * @param url - The full URL to fetch from
 * @returns The JSON response from the API
 */
export async function fetchFromPolygon(url: string): Promise<any> {
  try {
    console.log(`Fetching data from Polygon API: ${url.replace(/apiKey=([^&]+)/, 'apiKey=REDACTED')}`);

    const startTime = Date.now();
    const response = await fetch(url);
    const endTime = Date.now();

    console.log(`Polygon API response received in ${endTime - startTime}ms with status: ${response.status}`);

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`Polygon API error details: ${errorText}`);
      throw new Error(`Polygon API error: ${response.status} ${response.statusText} - ${errorText}`);
    }

    const data = await response.json();
    console.log(`Polygon API data received: ${JSON.stringify(data).substring(0, 100)}...`);

    return data;
  } catch (error) {
    console.error(`Error fetching from Polygon API:`, error);
    throw error;
  }
}

/**
 * Fetch historical price data from Polygon API
 * @param symbol - The ticker symbol
 * @param timeframe - Timeframe for the data (e.g., 'day', 'hour', '15minute')
 * @param apiKey - Polygon API key
 * @param limit - Number of data points to fetch (default: 100)
 * @returns Historical price data
 */
export async function fetchHistoricalPriceData(
  symbol: string,
  timeframe: string,
  apiKey: string,
  limit: number = 100
): Promise<any> {
  try {
    // Determine the appropriate endpoint based on the timeframe
    let multiplier = '1';
    let timespan = 'day';

    if (timeframe === 'day') {
      multiplier = '1';
      timespan = 'day';
    } else if (timeframe === 'hour') {
      multiplier = '1';
      timespan = 'hour';
    } else if (timeframe === '15minute') {
      multiplier = '15';
      timespan = 'minute';
    } else {
      throw new Error(`Unsupported timeframe: ${timeframe}`);
    }

    // Calculate date range (last 100 days by default)
    const endDate = new Date().toISOString().split('T')[0];
    const startDate = new Date(Date.now() - limit * 24 * 60 * 60 * 1000).toISOString().split('T')[0];

    // Construct the API URL
    const url = `https://api.polygon.io/v2/aggs/ticker/${symbol}/range/${multiplier}/${timespan}/${startDate}/${endDate}?apiKey=${apiKey}&limit=${limit}`;

    // Fetch data from Polygon API
    const response = await fetchFromPolygon(url);

    // Transform the response into a more usable format
    return {
      symbol,
      data: (response.results || []).map((bar: any) => ({
        date: new Date(bar.t).toISOString(),
        open: bar.o,
        high: bar.h,
        low: bar.l,
        close: bar.c,
        volume: bar.v,
        timestamp: bar.t
      }))
    };
  } catch (error) {
    console.error(`Error fetching historical data for ${symbol}:`, error);
    return { symbol, data: [] };
  }
}

/**
 * Fetch current price data from Polygon API
 * @param symbol - The ticker symbol
 * @param apiKey - Polygon API key
 * @returns Current price data
 */
export async function fetchCurrentPriceData(
  symbol: string,
  apiKey: string
): Promise<any> {
  try {
    // Construct the API URL for last quote
    const url = `https://api.polygon.io/v2/last/trade/${symbol}?apiKey=${apiKey}`;

    // Fetch data from Polygon API
    const response = await fetchFromPolygon(url);

    // Transform the response into a more usable format
    return {
      symbol,
      price: response.results?.p || 0,
      size: response.results?.s || 0,
      timestamp: response.results?.t || 0
    };
  } catch (error) {
    console.error(`Error fetching current price for ${symbol}:`, error);
    return { symbol, price: 0, size: 0, timestamp: 0 };
  }
}

/**
 * Fetch RSI data directly from Polygon API
 * @param symbol - The ticker symbol
 * @param timespan - Timeframe for the data (e.g., 'day', 'hour', '15minute')
 * @param apiKey - Polygon API key
 * @param window - The window size for RSI calculation (default: 14)
 * @param limit - Number of data points to fetch (default: 100)
 * @returns RSI data
 */
export async function fetchRsiData(
  symbol: string,
  timespan: string,
  apiKey: string,
  window: number = 14,
  limit: number = 100
): Promise<any> {
  try {
    console.log(`Fetching RSI data for ${symbol} with timespan ${timespan} and window ${window}`);

    // Construct the API URL for RSI
    const url = `https://api.polygon.io/v1/indicators/rsi/${symbol}?timespan=${timespan}&adjusted=true&window=${window}&series_type=close&order=desc&limit=${limit}&apiKey=${apiKey}`;

    // Fetch data from Polygon API
    const response = await fetchFromPolygon(url);

    if (!response.results || !response.results.values) {
      console.warn(`No RSI data received for ${symbol}`);
      return [];
    }

    console.log(`Received ${response.results.values.length} RSI data points`);

    // Transform the response into a more usable format and ensure values are numeric
    return response.results.values.map((item: any) => {
      // Ensure the value is a number
      const value = item.value;
      if (typeof value === 'number') {
        return value;
      } else {
        console.warn(`RSI value is not a number: ${value}, converting to number`);
        const numericValue = Number(value);
        if (isNaN(numericValue)) {
          throw new Error(`Invalid RSI value from Polygon API that cannot be converted to number: ${value}`);
        }
        return numericValue;
      }
    }).reverse();
  } catch (error) {
    console.error(`Error fetching RSI data for ${symbol}:`, error);
    return [];
  }
}

/**
 * Fetch financial data from Polygon API
 * @param symbol - The ticker symbol
 * @param apiKey - Polygon API key
 * @returns Financial statements data with multiple periods
 */
export async function fetchFinancialData(
  symbol: string,
  apiKey: string
): Promise<any> {
  try {
    console.log(`Fetching financial data for ${symbol}`);

    // Fetch more reports to get both quarterly and annual data
    const url = `https://api.polygon.io/vX/reference/financials?ticker=${symbol}&limit=8&order=desc&sort=filing_date&apiKey=${apiKey}`;

    // Fetch data from Polygon API
    const response = await fetchFromPolygon(url);

    if (!response.results || response.results.length === 0) {
      console.warn(`No financial data received for ${symbol}`);
      return null;
    }

    // Organize reports by period type
    const quarterlyReports = response.results.filter(r => r.fiscal_period.startsWith('Q'));
    const annualReports = response.results.filter(r => r.fiscal_period === 'FY');

    // Get the most recent reports
    const latestQuarterly = quarterlyReports[0];
    const latestAnnual = annualReports[0];

    console.log(`Latest quarterly report: ${latestQuarterly?.fiscal_period} ${latestQuarterly?.fiscal_year}`);
    console.log(`Latest annual report: ${latestAnnual?.fiscal_period} ${latestAnnual?.fiscal_year}`);

    // Return structured data with both periods
    return {
      quarterly: latestQuarterly?.financials || null,
      annual: latestAnnual?.financials || null,
      all_reports: response.results,
      latest: latestQuarterly?.financials || latestAnnual?.financials // Fallback to most recent
    };
  } catch (error) {
    console.error(`Error fetching financial data for ${symbol}:`, error);
    return null;
  }
}

/**
 * Fetch fundamental data from Polygon API (legacy function)
 * @param symbol - The ticker symbol
 * @param apiKey - Polygon API key
 * @returns Fundamental data
 */
export async function fetchFundamentalData(
  symbol: string,
  apiKey: string
): Promise<any> {
  try {
    // Construct the API URL for financials
    const url = `https://api.polygon.io/vX/reference/financials?ticker=${symbol}&apiKey=${apiKey}`;

    // Fetch data from Polygon API
    const response = await fetchFromPolygon(url);

    // Return the raw response for now (we'll process it based on specific needs)
    return response.results || [];
  } catch (error) {
    console.error(`Error fetching fundamental data for ${symbol}:`, error);
    return [];
  }
}

/**
 * Determine which data to fetch based on agent configuration
 * @param agentConfig - The agent configuration
 * @returns Object with flags for required data
 */
function determineRequiredData(agentConfig: AgentConfig): {
  needsHistoricalPrice: boolean;
  needsCurrentPrice: boolean;
  needsFundamentals: boolean;
  needsFinancials: boolean;
  requiredIndicators: Set<string>;
} {
  let needsHistoricalPrice = false;
  let needsCurrentPrice = false;
  let needsFundamentals = false;
  let needsFinancials = false;
  const requiredIndicators = new Set<string>();

  console.log(`Determining required data for agent with ${agentConfig.blocks.length} blocks`);
  console.log(`Block types: ${agentConfig.blocks.map(b => b.type).join(', ')}`);

  // Analyze blocks to determine required data
  for (const block of agentConfig.blocks) {
    console.log(`Analyzing block type: ${block.type}`);

    switch (block.type) {
      case BlockType.PRICE:
        if (block.lookback && block.lookback > 0) {
          console.log(`Block requires historical price data with lookback: ${block.lookback}`);
          needsHistoricalPrice = true;
        } else {
          console.log(`Block requires current price data`);
          needsCurrentPrice = true;
        }
        break;
      case BlockType.INDICATOR:
        const indicatorName = (block as any).indicator;
        console.log(`Block requires indicator: ${indicatorName}`);

        if (!indicatorName) {
          console.error(`Block ${block.id} has type INDICATOR but no indicator name`);
        } else {
          console.log(`Adding indicator ${indicatorName} to required indicators`);
          needsHistoricalPrice = true;
          requiredIndicators.add(indicatorName.toLowerCase());
        }
        break;
      case BlockType.FUNDAMENTAL:
        const fundamentalBlock = block as any;
        console.log(`Block requires fundamental data: ${fundamentalBlock.metric} from ${fundamentalBlock.statement}`);

        // Check if this is a new-style financial statement block or legacy fundamental
        if (fundamentalBlock.statement) {
          needsFinancials = true;
        } else {
          needsFundamentals = true;
        }
        break;
      default:
        console.log(`Block type ${block.type} does not require Polygon data`);
    }
  }

  console.log(`Required data summary:
    - Historical Price: ${needsHistoricalPrice}
    - Current Price: ${needsCurrentPrice}
    - Fundamentals: ${needsFundamentals}
    - Financials: ${needsFinancials}
    - Indicators: ${Array.from(requiredIndicators).join(', ')}
  `);

  return {
    needsHistoricalPrice,
    needsCurrentPrice,
    needsFundamentals,
    needsFinancials,
    requiredIndicators
  };
}

/**
 * Fetch all required data from Polygon API based on agent configuration
 * @param symbol - The ticker symbol
 * @param timeframe - Timeframe for the data
 * @param apiKey - Polygon API key
 * @param agentConfig - The agent configuration
 * @returns All data required for agent execution
 */


export async function fetchPolygonData(
  symbol: string,
  timeframe: string,
  apiKey: string,
  agentConfig: AgentConfig
): Promise<PolygonData> {
  console.log(`Starting fetchPolygonData for symbol: ${symbol}, timeframe: ${timeframe}`);

  // Check if API key is valid
  if (!apiKey) {
    console.error('Polygon API key is missing or empty - REAL DATA REQUIRED FOR SCANNER');
    throw new Error('POLYGON_API_KEY environment variable is not set. Real market data is required for the scanner to work properly.');
  } else {
    console.log('Polygon API key is present');
  }

  // Determine what data we need to fetch
  const {
    needsHistoricalPrice,
    needsCurrentPrice,
    needsFundamentals,
    needsFinancials,
    requiredIndicators
  } = determineRequiredData(agentConfig);

  // Initialize result object
  const result: PolygonData = {
    price: {
      current: 0,
      open: 0,
      high: 0,
      low: 0,
      close: 0,
      volume: 0,
      timestamp: 0
    },
    historical: {
      open: [],
      high: [],
      low: [],
      close: [],
      volume: [],
      timestamp: []
    },
    indicators: {},
    fundamentals: {}
  };

  // Fetch historical price data if needed
  if (needsHistoricalPrice) {
    console.log(`Fetching historical price data for ${symbol} with timeframe ${timeframe}`);
    const historicalData = await fetchHistoricalPriceData(symbol, timeframe, apiKey);

    if (historicalData.data && historicalData.data.length > 0) {
      console.log(`Received ${historicalData.data.length} historical data points`);

      // Transform data into arrays for easier processing
      result.historical.open = historicalData.data.map((bar: any) => bar.open);
      result.historical.high = historicalData.data.map((bar: any) => bar.high);
      result.historical.low = historicalData.data.map((bar: any) => bar.low);
      result.historical.close = historicalData.data.map((bar: any) => bar.close);
      result.historical.volume = historicalData.data.map((bar: any) => bar.volume);
      result.historical.timestamp = historicalData.data.map((bar: any) => bar.timestamp);

      // Set current price data from the latest historical data point
      const latest = historicalData.data[historicalData.data.length - 1];
      result.price.current = latest.close;
      result.price.open = latest.open;
      result.price.high = latest.high;
      result.price.low = latest.low;
      result.price.close = latest.close;
      result.price.volume = latest.volume;
      result.price.timestamp = latest.timestamp;

      console.log(`Latest price for ${symbol}: ${latest.close} (as of ${new Date(latest.timestamp).toISOString()})`);
    } else {
      console.warn(`No historical data received for ${symbol}`);
    }

    // Fetch or calculate required indicators
    if (requiredIndicators.size > 0) {
      console.log(`Processing indicators: ${Array.from(requiredIndicators).join(', ')}`);
      result.indicators = {};

      // Process each indicator
      for (const indicator of requiredIndicators) {
        console.log(`Processing indicator: ${indicator}`);

        // Handle support and resistance calculations (not available from Polygon API)
        if (indicator.toLowerCase() === 'support' || indicator.toLowerCase() === 'resistance') {
          console.log(`Calculating ${indicator} from historical data`);

          // Support and resistance are calculated in the agent executor, not here
          // We just need to mark them as available so they can be processed later
          result.indicators[indicator.toLowerCase()] = 'calculated_in_executor';

        } else if (indicator.toLowerCase() === 'candle_pattern') {
          console.log(`Detecting candle patterns from historical data`);

          // Candle patterns are calculated in the agent executor, not here
          // We just need to mark them as available so they can be processed later
          result.indicators[indicator.toLowerCase()] = 'calculated_in_executor';

        } else if (indicator.toLowerCase() === 'rsi') {
          try {
            console.log(`Fetching RSI data from Polygon API for ${symbol}`);
            const rsiData = await fetchRsiData(symbol, timeframe, apiKey);

            if (rsiData && rsiData.length > 0) {
              console.log(`RSI data fetched successfully, ${rsiData.length} data points`);

              // Ensure all RSI values are numbers
              const numericRsiData = rsiData.map((value: any) => {
                if (typeof value === 'number') {
                  return value;
                } else {
                  console.warn(`RSI value is not a number: ${value}, converting to number`);
                  const numericValue = Number(value);
                  if (isNaN(numericValue)) {
                    throw new Error(`Invalid RSI value that cannot be converted to number: ${value}`);
                  }
                  return numericValue;
                }
              });

              result.indicators.rsi = numericRsiData;
              console.log(`Latest RSI value: ${numericRsiData[numericRsiData.length - 1]}`);
            } else {
              console.error(`No RSI data received from Polygon API - FAILING (no fallback allowed)`);
              throw new Error(`Failed to fetch RSI data from Polygon API for ${symbol}`);
            }
          } catch (error) {
            console.error(`Error fetching RSI data:`, error);
            throw new Error(`Failed to fetch RSI data from Polygon API for ${symbol}: ${error.message}`);
          }
        } else {
          // Only RSI, support, resistance, and candle_pattern are supported
          // All other indicators must come from Polygon API - no fallback calculation
          console.error(`Unsupported indicator: ${indicator} - only RSI, support, resistance, and candle_pattern are supported`);
          throw new Error(`Unsupported indicator: ${indicator}. Only RSI (from Polygon API), support, resistance, and candle_pattern are supported.`);
        }
      }

      // Log indicator results (no sample data to avoid confusion)
      console.log(`Indicators processed successfully`);
    } else {
      console.log(`No indicators required`);
    }
  }

  // Fetch current price data if needed and not already set
  if (needsCurrentPrice && result.price.current === 0) {
    const currentData = await fetchCurrentPriceData(symbol, apiKey);

    if (currentData.price) {
      result.price.current = currentData.price;
      result.price.timestamp = currentData.timestamp;
    }
  }

  // Fetch fundamental data if needed
  if (needsFundamentals) {
    const fundamentalData = await fetchFundamentalData(symbol, apiKey);

    if (fundamentalData.length > 0) {
      // Process fundamental data into a more usable format
      const latest = fundamentalData[0];

      // Extract key metrics
      result.fundamentals = {
        peRatio: latest.ratios?.peRatio || null,
        pbRatio: latest.ratios?.pbRatio || null,
        debtToEquity: latest.ratios?.debtToEquity || null,
        revenueGrowth: latest.growth?.revenueGrowth || null,
        epsGrowth: latest.growth?.epsGrowth || null,
        dividendYield: latest.dividends?.dividendYield || null,
        marketCap: latest.marketCap || null,
        // Add more metrics as needed
      };
    }
  }

  // Fetch financial data if needed
  if (needsFinancials) {
    console.log(`Fetching financial data for ${symbol}`);
    const financialData = await fetchFinancialData(symbol, apiKey);

    if (financialData) {
      result.financials = financialData;
      console.log(`Financial data fetched successfully for ${symbol}`);

      // Log available financial statements
      const statements = Object.keys(financialData);
      console.log(`Available financial statements: ${statements.join(', ')}`);
    } else {
      console.warn(`No financial data available for ${symbol}`);
    }
  }

  return result;
}
