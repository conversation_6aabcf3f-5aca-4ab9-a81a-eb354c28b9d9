// Agent Executor
// Logic to execute trading agents

import {
  <PERSON>Config,
  AgentResult,
  BlockType,
  BlockUnion,
  PolygonData
} from "./agent-types.ts";
import { calculateRSI } from "./indicators.ts";

/**
 * Calculate support level from historical price data
 * @param polygonData - Data from Polygon API
 * @param parameters - Parameters for support calculation
 * @returns The support level
 */
function calculateSupportLevel(polygonData: PolygonData, parameters: any): number {
  const timeframe = parameters?.timeframe || 'month';
  const strength = parameters?.strength || 2;

  console.log(`Calculating support level with timeframe: ${timeframe}, strength: ${strength}`);

  // Get current price for validation
  const currentPrice = polygonData.price?.current || polygonData.price?.close || 0;
  console.log(`Current price: ${currentPrice}`);

  console.log(`Historical data available:`, {
    hasHistorical: !!polygonData.historical,
    hasLow: !!polygonData.historical?.low,
    lowLength: polygonData.historical?.low?.length || 0,
    sampleLows: polygonData.historical?.low?.slice(-5) || []
  });

  // Calculate lookback periods based on timeframe (assuming daily data)
  const timeframeLookbacks = {
    'week': 7,
    'month': 30,
    '3month': 90,
    '6month': 180,
    'year': 365
  };

  const lookback = timeframeLookbacks[timeframe] || 30;
  console.log(`Using ${lookback} days lookback for ${timeframe} timeframe`);

  if (!polygonData.historical || !polygonData.historical.low || polygonData.historical.low.length < Math.min(lookback, 10)) {
    throw new Error(`Insufficient historical data for support calculation. Need at least ${Math.min(lookback, 10)} data points, got ${polygonData.historical?.low?.length || 0}`);
  }

  // Use available data up to the lookback period
  const availableData = Math.min(lookback, polygonData.historical.low.length);
  const lows = polygonData.historical.low.slice(-availableData);
  console.log(`Using ${lows.length} low prices for ${timeframe} support calculation:`, lows.slice(0, 5), '...');

  const supportLevels: { level: number; touches: number }[] = [];

  // Find potential support levels (local minima)
  for (let i = 1; i < lows.length - 1; i++) {
    if (lows[i] <= lows[i - 1] && lows[i] <= lows[i + 1]) {
      const level = lows[i];

      // CRITICAL: Support must be below current price
      if (level >= currentPrice) {
        console.log(`Skipping potential support at ${level.toFixed(2)} - above current price ${currentPrice.toFixed(2)}`);
        continue;
      }

      // Count how many times price touched this level (within 1% tolerance)
      let touches = 0;
      const tolerance = level * 0.01; // 1% tolerance

      for (const low of lows) {
        if (Math.abs(low - level) <= tolerance) {
          touches++;
        }
      }

      console.log(`Potential support at ${level.toFixed(2)} with ${touches} touches (need ${strength}) - below current price ✓`);

      if (touches >= strength) {
        supportLevels.push({ level, touches });
      }
    }
  }

  console.log(`Found ${supportLevels.length} valid support levels below current price:`, supportLevels);

  // Return the strongest support level (most touches), or the lowest low if none found
  if (supportLevels.length > 0) {
    // Filter to only include levels below current price and get the strongest
    const validSupports = supportLevels.filter(s => s.level < currentPrice);

    if (validSupports.length > 0) {
      const strongestSupport = validSupports.reduce((prev, current) =>
        current.touches > prev.touches ? current : prev
      );
      console.log(`Selected strongest support level: ${strongestSupport.level} with ${strongestSupport.touches} touches`);
      return strongestSupport.level;
    }
  }

  // Fallback to the lowest low in the period, but ensure it's below current price
  const lowestLow = Math.min(...lows);
  if (lowestLow < currentPrice) {
    console.log(`No strong support found, using lowest low: ${lowestLow}`);
    return lowestLow;
  } else {
    // If even the lowest low is above current price, use 95% of current price as emergency fallback
    const emergencySupport = currentPrice * 0.95;
    console.log(`Even lowest low (${lowestLow}) is above current price, using emergency support: ${emergencySupport}`);
    return emergencySupport;
  }
}

/**
 * Calculate resistance level from historical price data
 * @param polygonData - Data from Polygon API
 * @param parameters - Parameters for resistance calculation
 * @returns The resistance level
 */
function calculateResistanceLevel(polygonData: PolygonData, parameters: any): number {
  const timeframe = parameters?.timeframe || 'month';
  const strength = parameters?.strength || 2;

  console.log(`Calculating resistance level with timeframe: ${timeframe}, strength: ${strength}`);

  // Get current price for validation
  const currentPrice = polygonData.price?.current || polygonData.price?.close || 0;
  console.log(`Current price: ${currentPrice}`);

  console.log(`Historical data available:`, {
    hasHistorical: !!polygonData.historical,
    hasHigh: !!polygonData.historical?.high,
    highLength: polygonData.historical?.high?.length || 0,
    sampleHighs: polygonData.historical?.high?.slice(-5) || []
  });

  // Calculate lookback periods based on timeframe (assuming daily data)
  const timeframeLookbacks = {
    'week': 7,
    'month': 30,
    '3month': 90,
    '6month': 180,
    'year': 365
  };

  const lookback = timeframeLookbacks[timeframe] || 30;
  console.log(`Using ${lookback} days lookback for ${timeframe} timeframe`);

  if (!polygonData.historical || !polygonData.historical.high || polygonData.historical.high.length < Math.min(lookback, 10)) {
    throw new Error(`Insufficient historical data for resistance calculation. Need at least ${Math.min(lookback, 10)} data points, got ${polygonData.historical?.high?.length || 0}`);
  }

  // Use available data up to the lookback period
  const availableData = Math.min(lookback, polygonData.historical.high.length);
  const highs = polygonData.historical.high.slice(-availableData);
  console.log(`Using ${highs.length} high prices for ${timeframe} resistance calculation:`, highs.slice(0, 5), '...');

  const resistanceLevels: { level: number; touches: number }[] = [];

  // Find potential resistance levels (local maxima)
  for (let i = 1; i < highs.length - 1; i++) {
    if (highs[i] >= highs[i - 1] && highs[i] >= highs[i + 1]) {
      const level = highs[i];

      // CRITICAL: Resistance must be above current price
      if (level <= currentPrice) {
        console.log(`Skipping potential resistance at ${level.toFixed(2)} - below current price ${currentPrice.toFixed(2)}`);
        continue;
      }

      // Count how many times price touched this level (within 1% tolerance)
      let touches = 0;
      const tolerance = level * 0.01; // 1% tolerance

      for (const high of highs) {
        if (Math.abs(high - level) <= tolerance) {
          touches++;
        }
      }

      console.log(`Potential resistance at ${level.toFixed(2)} with ${touches} touches (need ${strength}) - above current price ✓`);

      if (touches >= strength) {
        resistanceLevels.push({ level, touches });
      }
    }
  }

  console.log(`Found ${resistanceLevels.length} valid resistance levels above current price:`, resistanceLevels);

  // Return the strongest resistance level (most touches), or the highest high if none found
  if (resistanceLevels.length > 0) {
    // Filter to only include levels above current price and get the strongest
    const validResistances = resistanceLevels.filter(r => r.level > currentPrice);

    if (validResistances.length > 0) {
      const strongestResistance = validResistances.reduce((prev, current) =>
        current.touches > prev.touches ? current : prev
      );
      console.log(`Selected strongest resistance level: ${strongestResistance.level} with ${strongestResistance.touches} touches`);
      return strongestResistance.level;
    }
  }

  // Fallback to the highest high in the period, but ensure it's above current price
  const highestHigh = Math.max(...highs);
  if (highestHigh > currentPrice) {
    console.log(`No strong resistance found, using highest high: ${highestHigh}`);
    return highestHigh;
  } else {
    // If even the highest high is below current price, use 105% of current price as emergency fallback
    const emergencyResistance = currentPrice * 1.05;
    console.log(`Even highest high (${highestHigh}) is below current price, using emergency resistance: ${emergencyResistance}`);
    return emergencyResistance;
  }
}

// Detect candle patterns from historical data
function detectCandlePattern(polygonData: PolygonData, parameters: any): number {
  const timeframe = parameters?.timeframe || 'daily';
  const pattern = parameters?.pattern || 'any';

  console.log(`Detecting candle pattern: ${pattern} on ${timeframe} timeframe`);

  console.log(`Historical data available:`, {
    hasHistorical: !!polygonData.historical,
    hasOHLC: !!(polygonData.historical?.open && polygonData.historical?.high && polygonData.historical?.low && polygonData.historical?.close),
    dataLength: polygonData.historical?.close?.length || 0
  });

  if (!polygonData.historical || !polygonData.historical.open || !polygonData.historical.high ||
      !polygonData.historical.low || !polygonData.historical.close) {
    throw new Error(`Insufficient OHLC data for candle pattern detection`);
  }

  // Convert historical data to candle format
  const candles: any[] = [];
  const { open, high, low, close } = polygonData.historical;

  for (let i = 0; i < close.length; i++) {
    candles.push({
      open: open[i],
      high: high[i],
      low: low[i],
      close: close[i],
      date: new Date() // We don't have dates in this format, but patterns don't need them
    });
  }

  console.log(`Converted ${candles.length} candles for pattern detection`);

  // Analyze the most recent candle(s) for patterns
  const recentCandles = candles.slice(-10); // Look at last 10 candles for context

  if (recentCandles.length < 3) {
    throw new Error(`Need at least 3 candles for pattern detection, got ${recentCandles.length}`);
  }

  // Detect patterns based on the specified pattern type
  const detectedPatterns = detectCandlePatterns(recentCandles, pattern);

  console.log(`Detected ${detectedPatterns.length} patterns:`, detectedPatterns);

  // Return 1 if pattern found, 0 if not found
  return detectedPatterns.length > 0 ? 1 : 0;
}

// Helper function to detect specific candle patterns
function detectCandlePatterns(candles: any[], patternType: string): any[] {
  const patterns: any[] = [];
  const lastIndex = candles.length - 1;

  // Helper functions for candle analysis
  const bodySize = (candle: any) => Math.abs(candle.close - candle.open);
  const candleRange = (candle: any) => candle.high - candle.low;
  const upperWick = (candle: any) => candle.high - Math.max(candle.open, candle.close);
  const lowerWick = (candle: any) => Math.min(candle.open, candle.close) - candle.low;
  const isBullish = (candle: any) => candle.close > candle.open;

  // Check the most recent candle and previous ones for patterns
  for (let i = Math.max(2, lastIndex - 2); i <= lastIndex; i++) {
    const candle = candles[i];
    const prevCandle = i > 0 ? candles[i - 1] : null;
    const prev2Candle = i > 1 ? candles[i - 2] : null;

    // Doji pattern
    if (patternType === 'any' || patternType === 'doji' || patternType === 'reversal') {
      const bodyToRangeRatio = bodySize(candle) / candleRange(candle);
      if (bodyToRangeRatio < 0.1) {
        patterns.push({ pattern: 'doji', position: i, bullish: null });
      }
    }

    // Hammer pattern
    if (patternType === 'any' || patternType === 'hammer' || patternType === 'bullish' || patternType === 'reversal') {
      const body = bodySize(candle);
      const range = candleRange(candle);
      const lower = lowerWick(candle);

      if (body / range < 0.3 && lower / range > 0.6) {
        patterns.push({ pattern: 'hammer', position: i, bullish: true });
      }
    }

    // Shooting star pattern
    if (patternType === 'any' || patternType === 'shooting_star' || patternType === 'bearish' || patternType === 'reversal') {
      const body = bodySize(candle);
      const range = candleRange(candle);
      const upper = upperWick(candle);

      if (body / range < 0.3 && upper / range > 0.6) {
        patterns.push({ pattern: 'shooting_star', position: i, bullish: false });
      }
    }

    // Engulfing pattern (requires previous candle)
    if (prevCandle && (patternType === 'any' || patternType === 'engulfing' || patternType === 'bullish' || patternType === 'bearish' || patternType === 'reversal')) {
      // Bullish engulfing
      if (isBullish(candle) && !isBullish(prevCandle) &&
          candle.open < prevCandle.close && candle.close > prevCandle.open) {
        patterns.push({ pattern: 'bullish_engulfing', position: i, bullish: true });
      }

      // Bearish engulfing
      if (!isBullish(candle) && isBullish(prevCandle) &&
          candle.open > prevCandle.close && candle.close < prevCandle.open) {
        patterns.push({ pattern: 'bearish_engulfing', position: i, bullish: false });
      }
    }

    // Morning star pattern (requires 2 previous candles)
    if (prevCandle && prev2Candle && (patternType === 'any' || patternType === 'morning_star' || patternType === 'bullish' || patternType === 'reversal')) {
      if (!isBullish(prev2Candle) && bodySize(prev2Candle) / candleRange(prev2Candle) > 0.6 &&
          bodySize(prevCandle) / candleRange(prevCandle) < 0.3 &&
          isBullish(candle) && bodySize(candle) / candleRange(candle) > 0.6) {
        const firstMidpoint = prev2Candle.open - (bodySize(prev2Candle) / 2);
        if (candle.close > firstMidpoint) {
          patterns.push({ pattern: 'morning_star', position: i, bullish: true });
        }
      }
    }

    // Evening star pattern (requires 2 previous candles)
    if (prevCandle && prev2Candle && (patternType === 'any' || patternType === 'evening_star' || patternType === 'bearish' || patternType === 'reversal')) {
      if (isBullish(prev2Candle) && bodySize(prev2Candle) / candleRange(prev2Candle) > 0.6 &&
          bodySize(prevCandle) / candleRange(prevCandle) < 0.3 &&
          !isBullish(candle) && bodySize(candle) / candleRange(candle) > 0.6) {
        const firstMidpoint = prev2Candle.open + (bodySize(prev2Candle) / 2);
        if (candle.close < firstMidpoint) {
          patterns.push({ pattern: 'evening_star', position: i, bullish: false });
        }
      }
    }
  }

  // Filter patterns based on bullish/bearish preference
  if (patternType === 'bullish') {
    return patterns.filter(p => p.bullish === true);
  } else if (patternType === 'bearish') {
    return patterns.filter(p => p.bullish === false);
  }

  return patterns;
}

/**
 * Execute a block and return its result
 * @param block - The block to execute
 * @param blockResults - Results of previously executed blocks
 * @param polygonData - Data from Polygon API
 * @param inheritedValue - Value passed from a previous condition block in the chain
 * @returns The result of executing the block
 */
function executeBlock(
  block: BlockUnion,
  blockResults: Map<string, any>,
  polygonData: PolygonData,
  inheritedValue?: any
): any {
  switch (block.type) {
    case BlockType.WHEN_RUN:
      // When Run block is just an entry point, return true to indicate it executed
      return true;
    case BlockType.INDICATOR:
      return executeIndicatorBlock(block, polygonData);
    case BlockType.PRICE:
      return executePriceBlock(block, polygonData);
    case BlockType.FUNDAMENTAL:
      return executeFundamentalBlock(block, polygonData);
    case BlockType.CONDITION:
      return executeConditionBlock(block, blockResults, polygonData, inheritedValue);
    case BlockType.TRIGGER:
      return executeTriggerBlock(block, blockResults);
    case BlockType.OPERATOR:
      return executeOperatorBlock(block, blockResults);
    case BlockType.PERCENTAGE_UP:
      return executePercentageUpBlock(block, polygonData);
    case BlockType.PERCENTAGE_DOWN:
      return executePercentageDownBlock(block, polygonData);
    default:
      throw new Error(`Unknown block type: ${(block as any).type}`);
  }
}

/**
 * Execute an indicator block
 * @param block - The indicator block to execute
 * @param polygonData - Data from Polygon API
 * @returns The indicator value
 */
function executeIndicatorBlock(block: any, polygonData: PolygonData): any {
  const { indicator, parameters } = block;

  console.log(`Executing indicator block for ${indicator}`);

  // Normalize the indicator name
  const normalizedIndicator = indicator.toLowerCase().trim();

  // Check if the indicator exists in the data or needs to be calculated
  if (!polygonData.indicators[normalizedIndicator]) {
    console.error(`Indicator not found: ${normalizedIndicator}`);
    console.log(`Available indicators: ${Object.keys(polygonData.indicators).join(', ')}`);

    throw new Error(`Indicator '${normalizedIndicator}' not found in polygon data. Available indicators: ${Object.keys(polygonData.indicators).join(', ')}`);
  }

  // Handle special case where support/resistance/candle_pattern are marked for calculation
  if (polygonData.indicators[normalizedIndicator] === 'calculated_in_executor') {
    console.log(`Calculating ${normalizedIndicator} in executor`);

    if (normalizedIndicator === 'support') {
      return calculateSupportLevel(polygonData, parameters);
    } else if (normalizedIndicator === 'resistance') {
      return calculateResistanceLevel(polygonData, parameters);
    } else if (normalizedIndicator === 'candle_pattern') {
      return detectCandlePattern(polygonData, parameters);
    }
  }

  // Get the indicator data
  const indicatorData = polygonData.indicators[normalizedIndicator];
  console.log(`Found indicator data for ${normalizedIndicator}: ${typeof indicatorData}`);

  // Log a sample of the indicator data
  if (Array.isArray(indicatorData)) {
    console.log(`Indicator data sample: ${indicatorData.slice(-5).join(', ')}`);
  } else if (typeof indicatorData === 'object') {
    console.log(`Indicator data keys: ${Object.keys(indicatorData).join(', ')}`);
  }

  // Return the latest value by default, or apply specific logic based on the indicator
  switch (normalizedIndicator) {
    case 'rsi':
      // Return the latest RSI value
      if (Array.isArray(indicatorData) && indicatorData.length > 0) {
        const latestRsi = indicatorData[indicatorData.length - 1];
        console.log(`Latest RSI value: ${latestRsi}`);

        // Ensure we return a number
        if (typeof latestRsi === 'number') {
          return latestRsi;
        } else {
          console.warn(`RSI value is not a number: ${latestRsi}, converting to number`);
          const numericRsi = Number(latestRsi);
          if (!isNaN(numericRsi)) {
            return numericRsi;
          }
        }
      }

      // If we get here, either there's no data or it couldn't be converted to a number
      console.warn(`RSI data is not in expected format or couldn't be converted to a number, returning default value`);

      // Try to get RSI from raw data if available
      try {
        if (polygonData.historical && polygonData.historical.close && polygonData.historical.close.length > 0) {
          console.log("Calculating RSI from historical close prices");
          const calculatedRsi = calculateRSI(polygonData.historical.close);
          if (calculatedRsi && calculatedRsi.length > 0) {
            const latestCalculatedRsi = calculatedRsi[calculatedRsi.length - 1];
            if (!isNaN(latestCalculatedRsi)) {
              console.log(`Calculated RSI value: ${latestCalculatedRsi}`);
              return latestCalculatedRsi;
            }
          }
        }
      } catch (error) {
        console.error("Error calculating RSI from historical data:", error);
      }

      // Throw an error instead of returning a default value
      throw new Error(`RSI data is not in expected format or couldn't be converted to a number. Data: ${JSON.stringify(indicatorData)}`);
    case 'macd':
      // Return the latest MACD values
      if (indicatorData && indicatorData.macd && indicatorData.signal && indicatorData.histogram) {
        return {
          macd: indicatorData.macd[indicatorData.macd.length - 1],
          signal: indicatorData.signal[indicatorData.signal.length - 1],
          histogram: indicatorData.histogram[indicatorData.histogram.length - 1]
        };
      } else {
        console.warn(`MACD data is not in expected format, returning default values`);
        return { macd: 0, signal: 0, histogram: 0 };
      }
    case 'bollinger':
      // Return the latest Bollinger Bands values
      if (indicatorData && indicatorData.upper && indicatorData.middle && indicatorData.lower) {
        return {
          upper: indicatorData.upper[indicatorData.upper.length - 1],
          middle: indicatorData.middle[indicatorData.middle.length - 1],
          lower: indicatorData.lower[indicatorData.lower.length - 1]
        };
      } else {
        console.warn(`Bollinger Bands data is not in expected format, returning default values`);
        return { upper: 0, middle: 0, lower: 0 };
      }
    case 'sma':
      // Return the specified SMA value
      const period = parameters?.period || 20;
      const smaKey = `sma${period}`;
      if (indicatorData && indicatorData[smaKey] && indicatorData[smaKey].length > 0) {
        return indicatorData[smaKey][indicatorData[smaKey].length - 1];
      } else {
        console.warn(`SMA data is not in expected format, returning default value`);
        return 0;
      }
    case 'ema':
      // Return the specified EMA value
      const emaPeriod = parameters?.period || 20;
      const emaKey = `ema${emaPeriod}`;
      if (indicatorData && indicatorData[emaKey] && indicatorData[emaKey].length > 0) {
        return indicatorData[emaKey][indicatorData[emaKey].length - 1];
      } else {
        console.warn(`EMA data is not in expected format, returning default value`);
        return 0;
      }

    default:
      // For other indicators, return the latest value with error handling
      console.log(`Using default handling for indicator: ${normalizedIndicator}`);
      if (Array.isArray(indicatorData) && indicatorData.length > 0) {
        return indicatorData[indicatorData.length - 1];
      } else if (typeof indicatorData === 'number') {
        return indicatorData;
      } else if (typeof indicatorData === 'object' && indicatorData !== null) {
        // Try to find a numeric value in the object
        for (const key in indicatorData) {
          if (Array.isArray(indicatorData[key]) && indicatorData[key].length > 0) {
            return indicatorData[key][indicatorData[key].length - 1];
          } else if (typeof indicatorData[key] === 'number') {
            return indicatorData[key];
          }
        }
      }

      // If all else fails, return a default value
      console.warn(`Could not extract a value from ${normalizedIndicator}, returning default value`);
      return 0;
  }
}

/**
 * Execute a price block
 * @param block - The price block to execute
 * @param polygonData - Data from Polygon API
 * @returns The price value
 */
function executePriceBlock(block: any, polygonData: PolygonData): any {
  const { dataPoint, lookback } = block;

  // If lookback is specified, get historical data
  if (lookback && lookback > 0) {
    const index = polygonData.historical[dataPoint].length - 1 - lookback;
    if (index < 0) {
      throw new Error(`Not enough historical data for lookback: ${lookback}`);
    }
    return polygonData.historical[dataPoint][index];
  }

  // Otherwise, get current price data
  return polygonData.price[dataPoint];
}

/**
 * Get value from financial data with period awareness
 * @param financials - Financial data (new structure with quarterly/annual)
 * @param path - Path like 'income_statement.net_income_loss'
 * @param period - 'quarterly', 'annual', or 'ttm' (trailing twelve months)
 * @returns The value or null if not found
 */
function getFinancialValue(financials: any, path: string, period: 'quarterly' | 'annual' | 'ttm' = 'quarterly'): number | null {
  const [statement, metric] = path.split('.');

  console.log(`getFinancialValue called with path: ${path}, period: ${period}`);
  console.log(`Financials structure:`, Object.keys(financials || {}));

  // Handle new structured financials data
  let sourceData = null;

  if (period === 'annual' && financials?.annual) {
    sourceData = financials.annual;
    console.log(`Using annual data for ${path}`);
  } else if (period === 'quarterly' && financials?.quarterly) {
    sourceData = financials.quarterly;
    console.log(`Using quarterly data for ${path}`);
  } else if (period === 'ttm') {
    // For TTM, we'll use quarterly data and multiply by 4 for income statement items
    // or use annual data if available and more recent
    if (financials?.annual && financials?.quarterly) {
      // Use annual if it's more recent, otherwise use quarterly * 4
      sourceData = financials.annual;
      console.log(`Using annual data for TTM calculation of ${path}`);
    } else if (financials?.quarterly) {
      sourceData = financials.quarterly;
      console.log(`Using quarterly data for TTM calculation of ${path} (will multiply by 4 for income statement)`);
    } else if (financials?.annual) {
      sourceData = financials.annual;
      console.log(`Using annual data as fallback for TTM calculation of ${path}`);
    }
  } else {
    // Fallback to latest available data or old structure
    sourceData = financials?.latest || financials;
    console.log(`Using fallback data for ${path}`);
  }

  if (!sourceData || !sourceData[statement]) {
    console.error(`Financial statement not found: ${statement} for period ${period}`);
    return null;
  }

  const metricData = sourceData[statement][metric];
  if (!metricData) {
    console.error(`Metric not found in ${statement}: ${metric} for period ${period}`);
    return null;
  }

  // Return the value, handling different data structures
  let value = null;
  if (typeof metricData === 'object' && metricData.value !== undefined) {
    value = metricData.value;
  } else if (typeof metricData === 'number') {
    value = metricData;
  } else {
    console.error(`Unexpected metric data structure for ${metric}:`, metricData);
    return null;
  }

  // For TTM calculations using quarterly data, multiply income statement items by 4
  if (period === 'ttm' && sourceData === financials.quarterly && statement === 'income_statement') {
    console.log(`Converting quarterly ${metric} to TTM: ${value} * 4 = ${value * 4}`);
    value = value * 4;
  }

  return value;
}

/**
 * Calculate financial ratios and metrics
 * @param metric - The metric to calculate
 * @param polygonData - Data from Polygon API
 * @returns The calculated value
 */
function calculateFinancialMetric(metric: string, polygonData: PolygonData): number | null {
  const { financials, price } = polygonData;

  if (!financials) {
    console.error('No financials data available for calculation');
    return null;
  }

  console.log(`Calculating metric: ${metric}`);

  switch (metric) {
    // Profitability Ratios
    case 'return_on_equity': {
      // Use TTM (trailing twelve months) net income with most recent equity for accurate ROE
      const netIncomeTTM = getFinancialValue(financials, 'income_statement.net_income_loss', 'ttm');
      const equity = getFinancialValue(financials, 'balance_sheet.equity_attributable_to_parent', 'quarterly'); // Most recent equity

      console.log(`ROE Debug - Net Income TTM: ${netIncomeTTM}, Equity: ${equity}`);

      if (netIncomeTTM !== null && equity !== null && netIncomeTTM !== undefined && equity !== undefined) {
        const roe = (netIncomeTTM / equity) * 100;
        console.log(`ROE Calculation Details (TTM):`);
        console.log(`  Net Income (TTM): $${netIncomeTTM.toLocaleString()}`);
        console.log(`  Shareholders' Equity (Latest): $${equity.toLocaleString()}`);
        console.log(`  ROE = (${netIncomeTTM.toLocaleString()} ÷ ${equity.toLocaleString()}) × 100 = ${roe.toFixed(2)}%`);

        // Return an object with both the calculated value and the components
        return {
          value: roe,
          components: {
            net_income_ttm: netIncomeTTM,
            shareholders_equity: equity,
            calculation: `(${netIncomeTTM.toLocaleString()} TTM ÷ ${equity.toLocaleString()}) × 100`,
            period_note: 'Using TTM Net Income with latest Shareholders\' Equity'
          }
        };
      } else {
        console.error(`ROE calculation failed - Net Income TTM: ${netIncomeTTM}, Equity: ${equity}`);
      }
      return null;
    }

    case 'return_on_assets': {
      // Use TTM net income with most recent assets
      const netIncomeTTM = getFinancialValue(financials, 'income_statement.net_income_loss', 'ttm');
      const assets = getFinancialValue(financials, 'balance_sheet.assets', 'quarterly');
      return (netIncomeTTM && assets) ? (netIncomeTTM / assets) * 100 : null;
    }

    case 'operating_margin': {
      // Use TTM operating income and revenue for consistent period
      const operatingIncomeTTM = getFinancialValue(financials, 'income_statement.operating_income_loss', 'ttm');
      const revenueTTM = getFinancialValue(financials, 'income_statement.revenues', 'ttm');
      return (operatingIncomeTTM && revenueTTM) ? (operatingIncomeTTM / revenueTTM) * 100 : null;
    }

    case 'net_profit_margin': {
      // Use TTM net income and revenue for consistent period
      const netIncomeTTM = getFinancialValue(financials, 'income_statement.net_income_loss', 'ttm');
      const revenueTTM = getFinancialValue(financials, 'income_statement.revenues', 'ttm');
      return (netIncomeTTM && revenueTTM) ? (netIncomeTTM / revenueTTM) * 100 : null;
    }

    case 'gross_margin': {
      // Use TTM gross profit and revenue for consistent period
      const grossProfitTTM = getFinancialValue(financials, 'income_statement.gross_profit', 'ttm');
      const revenueTTM = getFinancialValue(financials, 'income_statement.revenues', 'ttm');
      return (grossProfitTTM && revenueTTM) ? (grossProfitTTM / revenueTTM) * 100 : null;
    }

    // Liquidity Ratios
    case 'current_ratio': {
      // Use most recent balance sheet data
      const currentAssets = getFinancialValue(financials, 'balance_sheet.current_assets', 'quarterly');
      const currentLiabilities = getFinancialValue(financials, 'balance_sheet.current_liabilities', 'quarterly');
      return (currentAssets && currentLiabilities) ? currentAssets / currentLiabilities : null;
    }

    // Leverage Ratios
    case 'debt_to_equity': {
      // Use most recent balance sheet data
      const longTermDebt = getFinancialValue(financials, 'balance_sheet.long_term_debt', 'quarterly');
      const equity = getFinancialValue(financials, 'balance_sheet.equity_attributable_to_parent', 'quarterly');
      return (longTermDebt && equity) ? longTermDebt / equity : null;
    }

    case 'interest_coverage_ratio': {
      // Use TTM operating income and interest expense for consistent period
      const operatingIncomeTTM = getFinancialValue(financials, 'income_statement.operating_income_loss', 'ttm');
      const interestExpenseTTM = getFinancialValue(financials, 'income_statement.interest_expense_operating', 'ttm');
      return (operatingIncomeTTM && interestExpenseTTM) ? operatingIncomeTTM / interestExpenseTTM : null;
    }

    // Cash Flow Metrics
    case 'free_cash_flow': {
      // Use TTM cash flow data for consistent period
      const operatingCashFlowTTM = getFinancialValue(financials, 'cash_flow_statement.net_cash_flow_from_operating_activities', 'ttm');
      const investingCashFlowTTM = getFinancialValue(financials, 'cash_flow_statement.net_cash_flow_from_investing_activities', 'ttm');
      // Estimate CapEx as absolute value of investing cash flow (simplified)
      const capex = investingCashFlowTTM ? Math.abs(investingCashFlowTTM) : 0;
      return operatingCashFlowTTM ? operatingCashFlowTTM - capex : null;
    }

    case 'capital_expenditures': {
      // Use TTM investing cash flow
      const investingCashFlowTTM = getFinancialValue(financials, 'cash_flow_statement.net_cash_flow_from_investing_activities', 'ttm');
      return investingCashFlowTTM ? Math.abs(investingCashFlowTTM) : null;
    }

    // Per Share Metrics
    case 'book_value_per_share': {
      // Use most recent balance sheet equity and shares outstanding
      const equity = getFinancialValue(financials, 'balance_sheet.equity_attributable_to_parent', 'quarterly');
      const shares = getFinancialValue(financials, 'income_statement.basic_average_shares', 'quarterly');
      return (equity && shares) ? equity / shares : null;
    }

    // Valuation Ratios (require market data)
    case 'price_to_earnings': {
      // Use TTM EPS for P/E calculation
      const epsTTM = getFinancialValue(financials, 'income_statement.basic_earnings_per_share', 'ttm');
      const currentPrice = price?.current;
      return (epsTTM && currentPrice) ? currentPrice / epsTTM : null;
    }

    // Efficiency Ratios
    case 'inventory_turnover': {
      // Use TTM cost of revenue with most recent inventory
      const costOfRevenueTTM = getFinancialValue(financials, 'income_statement.cost_of_revenue', 'ttm');
      const inventory = getFinancialValue(financials, 'balance_sheet.inventory', 'quarterly');
      return (costOfRevenueTTM && inventory) ? costOfRevenueTTM / inventory : null;
    }

    // Direct metrics (just pass through)
    case 'cash_and_equivalents': {
      // Use most recent cash position
      return getFinancialValue(financials, 'balance_sheet.cash', 'quarterly');
    }

    default:
      console.error(`Unknown calculated metric: ${metric}`);
      return null;
  }
}

/**
 * Execute a fundamental block
 * @param block - The fundamental block to execute
 * @param polygonData - Data from Polygon API
 * @returns The fundamental value
 */
function executeFundamentalBlock(block: any, polygonData: PolygonData): any {
  const { metric, statement, period } = block;

  console.log(`Executing fundamental block for metric: ${metric}, statement: ${statement}, period: ${period}`);

  // Check if this is a calculated metric
  if (statement === 'calculated') {
    return calculateFinancialMetric(metric, polygonData);
  }

  // Handle direct financial statement metrics
  if (!polygonData.financials) {
    console.error('No financials data available');
    return null;
  }

  // Navigate to the correct statement (balance_sheet, income_statement, cash_flow_statement, comprehensive_income)
  const statementData = polygonData.financials[statement || 'income_statement'];
  if (!statementData) {
    console.error(`Financial statement not found: ${statement}`);
    return null;
  }

  // Get the metric from the statement
  const metricData = statementData[metric];
  if (!metricData) {
    console.error(`Metric not found in ${statement}: ${metric}`);
    return null;
  }

  // Return the value, handling different data structures
  if (typeof metricData === 'object' && metricData.value !== undefined) {
    return metricData.value;
  } else if (typeof metricData === 'number') {
    return metricData;
  } else {
    console.error(`Unexpected metric data structure for ${metric}:`, metricData);
    return null;
  }
}

/**
 * Execute a condition block
 * @param block - The condition block to execute
 * @param blockResults - Results of previously executed blocks
 * @param polygonData - Data from Polygon API
 * @param inheritedValue - Value passed from a previous condition block in the chain
 * @returns The result of the condition (true or false)
 */
function executeConditionBlock(
  block: any,
  blockResults: Map<string, any>,
  polygonData: PolygonData,
  inheritedValue?: any
): boolean {
  const { operator, inputConnections, compareValue } = block;

  console.log(`Executing condition block with operator: ${operator}`);
  console.log(`Compare value: ${compareValue}`);
  console.log(`Input connections: ${inputConnections.join(', ')}`);
  console.log(`Inherited value: ${inheritedValue}`);

  let valueToCompare: any;
  let comparisonValue = compareValue;
  let inputValues: any[] = []; // Initialize inputValues to avoid undefined errors

  // Always get input values from connected blocks first
  inputValues = inputConnections.map(id => {
    if (!blockResults.has(id)) {
      throw new Error(`Block result not found for ID: ${id}`);
    }
    const value = blockResults.get(id);
    console.log(`Input value from block ${id}: ${value}`);
    return value;
  });

  // For AND/OR/NOT operations, we evaluate the input values directly as boolean logic
  if (operator === 'and' || operator === 'or' || operator === 'not') {
    console.log(`${operator.toUpperCase()} operation with ${inputValues.length} input values: [${inputValues.join(', ')}]`);

    // For logical operations, we don't need valueToCompare or compareValue
    // We'll evaluate the inputValues directly in the switch statement
  } else {
    // For comparison operations (>, <, ==, etc.), use inherited value if available, otherwise first input value
    if (inheritedValue !== undefined) {
      valueToCompare = inheritedValue;
      console.log(`Using inherited value: ${valueToCompare}`);
    } else {
      valueToCompare = inputValues[0];
    }

    // Handle calculated metrics that return objects with value and components
    if (valueToCompare && typeof valueToCompare === 'object' && valueToCompare.value !== undefined) {
      console.log(`Value is a calculated metric with components:`, valueToCompare.components);
      valueToCompare = valueToCompare.value;
    }
  }

  // Ensure values are numeric for comparison operations
  if (typeof valueToCompare !== 'number') {
    console.warn(`Value to compare is not a number: ${valueToCompare}, attempting to convert`);

    // Special handling for boolean values
    if (valueToCompare === false) {
      console.log("Value is boolean false, converting to 0");
      valueToCompare = 0;


    } else {
      // For non-boolean values, try normal conversion
      const numericValue = Number(valueToCompare);
      if (!isNaN(numericValue)) {
        valueToCompare = numericValue;
      } else {
        console.error(`Could not convert value to number: ${valueToCompare}`);
        // Default to a value that will likely cause the condition to fail
        valueToCompare = 0;
      }
    }
  }

  if (typeof comparisonValue !== 'number') {
    console.warn(`Comparison value is not a number: ${comparisonValue}, attempting to convert`);
    const numericValue = Number(comparisonValue);
    if (!isNaN(numericValue)) {
      comparisonValue = numericValue;
    } else {
      console.error(`Could not convert comparison value to number: ${comparisonValue}`);
      // Default to a value that will likely cause the condition to fail
      comparisonValue = 0;
    }
  }

  console.log(`Value to compare (converted): ${valueToCompare}`);
  console.log(`Comparison value (converted): ${comparisonValue}`);

  console.log(`Evaluating condition: ${valueToCompare} ${operator} ${comparisonValue}`);



  // Evaluate the condition based on the operator
  let result;
  switch (operator) {
    case '>':
      result = valueToCompare > comparisonValue;
      console.log(`Evaluating ${valueToCompare} > ${comparisonValue} = ${result}`);
      break;
    case '<':
      result = valueToCompare < comparisonValue;
      console.log(`Evaluating ${valueToCompare} < ${comparisonValue} = ${result}`);
      break;
    case '==':
      result = valueToCompare === comparisonValue;
      console.log(`Evaluating ${valueToCompare} == ${comparisonValue} = ${result}`);
      break;
    case '>=':
      result = valueToCompare >= comparisonValue;
      console.log(`Evaluating ${valueToCompare} >= ${comparisonValue} = ${result}`);


      break;
    case '<=':
      result = valueToCompare <= comparisonValue;
      console.log(`Evaluating ${valueToCompare} <= ${comparisonValue} = ${result}`);
      break;
    case '!=':
      result = valueToCompare !== comparisonValue;
      console.log(`Evaluating ${valueToCompare} != ${comparisonValue} = ${result}`);
      break;
    case 'between':
      // For between, we need a third value
      const upperBound = (inputValues.length > 1 ? inputValues[1] : null) || comparisonValue + 10; // Default upper bound
      result = valueToCompare >= comparisonValue && valueToCompare <= upperBound;
      console.log(`Evaluating ${comparisonValue} <= ${valueToCompare} <= ${upperBound} = ${result}`);
      break;
    case 'and':
      // AND operation: ALL inputs must be true
      if (inputValues.length === 0) {
        console.error('AND operation with no input values');
        result = false;
      } else {
        // Convert all input values to boolean and check if ALL are true
        const booleanInputs = inputValues.map(val => !!val);
        result = booleanInputs.every(val => val === true);
        console.log(`AND operation: inputs [${inputValues.join(', ')}] → booleans [${booleanInputs.join(', ')}] → result: ${result}`);
      }
      break;
    case 'or':
      // OR operation: ANY input can be true
      if (inputValues.length === 0) {
        console.error('OR operation with no input values');
        result = false;
      } else {
        // Convert all input values to boolean and check if ANY are true
        const booleanInputs = inputValues.map(val => !!val);
        result = booleanInputs.some(val => val === true);
        console.log(`OR operation: inputs [${inputValues.join(', ')}] → booleans [${booleanInputs.join(', ')}] → result: ${result}`);
      }
      break;
    case 'not':
      result = !valueToCompare;
      console.log(`Evaluating NOT ${valueToCompare} = ${result}`);
      break;
    default:
      throw new Error(`Unknown operator: ${operator}`);
  }

  console.log(`Condition result: ${result}`);

  // Log which path will be taken
  if (result) {
    console.log(`Taking TRUE path to block: ${block.trueConnection}`);
  } else {
    console.log(`Taking FALSE path to block: ${block.falseConnection}`);
  }

  return result;
}

/**
 * Execute a trigger block
 * @param block - The trigger block to execute
 * @param blockResults - Results of previously executed blocks
 * @returns The trigger result
 */
function executeTriggerBlock(block: any, blockResults: Map<string, any>): any {
  const { signal, confidence, inputConnections } = block;

  console.log(`Executing trigger block with signal: ${signal}, confidence: ${confidence}`);
  console.log(`Input connections: ${JSON.stringify(inputConnections)}`);

  // If there are no input connections, this trigger was reached through a condition path
  // In this case, we should return the signal and confidence as-is
  if (!inputConnections || inputConnections.length === 0) {
    console.log(`Trigger block has no input connections, returning signal: ${signal}`);
    const result = {
      signal: signal,
      confidence: confidence
    };
    console.log(`Trigger result: signal=${result.signal}, confidence=${result.confidence}`);
    return result;
  }

  // Check if all input conditions are true
  const inputResults = inputConnections.map(id => {
    if (!blockResults.has(id)) {
      console.error(`Block result not found for ID: ${id}`);
      // Instead of throwing an error, return false for missing blocks
      return false;
    }
    const result = blockResults.get(id);
    console.log(`Input result from block ${id}: ${result}`);
    return result;
  });

  const allConditionsTrue = inputResults.every(result => !!result);
  console.log(`All conditions true: ${allConditionsTrue}`);

  // IMPORTANT: If this trigger was reached through a condition path (condition → trigger),
  // and the condition was true, then we should return the signal regardless of other inputs
  // This is because the condition already validated the trigger should fire

  // Check if any input is from a condition block that evaluated to true
  const hasValidCondition = inputResults.some(result => result === true);

  if (hasValidCondition) {
    console.log(`Trigger has valid condition input, returning signal: ${signal}`);
    const result = {
      signal: signal,
      confidence: confidence
    };
    console.log(`Trigger result: signal=${result.signal}, confidence=${result.confidence}`);
    return result;
  }

  // Fallback: Return the signal and confidence if all conditions are true
  const result = {
    signal: allConditionsTrue ? signal : 'neutral',
    confidence: allConditionsTrue ? confidence : 0
  };

  console.log(`Trigger result: signal=${result.signal}, confidence=${result.confidence}`);
  return result;
}

/**
 * Execute an operator block
 * @param block - The operator block to execute
 * @param blockResults - Results of previously executed blocks
 * @returns The result of the operation
 */
function executeOperatorBlock(block: any, blockResults: Map<string, any>): any {
  const { operation, inputConnections } = block;

  // Get input values
  const inputValues = inputConnections.map(id => {
    if (!blockResults.has(id)) {
      throw new Error(`Block result not found for ID: ${id}`);
    }
    return blockResults.get(id);
  });

  // Perform the operation
  switch (operation) {
    case 'add':
      return inputValues.reduce((sum, val) => sum + val, 0);
    case 'subtract':
      return inputValues.reduce((result, val, index) =>
        index === 0 ? val : result - val, 0);
    case 'multiply':
      return inputValues.reduce((product, val) => product * val, 1);
    case 'divide':
      return inputValues.reduce((result, val, index) =>
        index === 0 ? val : result / val, 0);
    case 'average':
      return inputValues.reduce((sum, val) => sum + val, 0) / inputValues.length;
    case 'min':
      return Math.min(...inputValues);
    case 'max':
      return Math.max(...inputValues);
    case 'abs':
      return Math.abs(inputValues[0]);
    default:
      throw new Error(`Unknown operation: ${operation}`);
  }
}

/**
 * Execute a percentage up block
 * @param block - The percentage up block to execute
 * @param polygonData - Data from Polygon API
 * @returns Boolean indicating if the percentage threshold was met
 */
function executePercentageUpBlock(block: any, polygonData: PolygonData): boolean {
  const { percentage } = block;

  // Get current price and calculate previous close from historical data
  const currentPrice = polygonData.price.current || polygonData.price.close;
  const historicalCloses = polygonData.historical.close;

  if (!currentPrice || !historicalCloses || historicalCloses.length < 2) {
    console.warn('Missing price data for percentage up block');
    return false;
  }

  // Use the second-to-last close as previous close (last close might be current)
  const previousClose = historicalCloses[historicalCloses.length - 2];

  if (!previousClose) {
    console.warn('Missing previous close data for percentage up block');
    return false;
  }

  // Calculate percentage change
  const percentageChange = ((currentPrice - previousClose) / previousClose) * 100;

  console.log(`Percentage Up Block: Current: ${currentPrice}, Previous: ${previousClose}, Change: ${percentageChange.toFixed(2)}%, Threshold: +${percentage}%`);

  // Return true if the percentage change meets or exceeds the threshold
  return percentageChange >= percentage;
}

/**
 * Execute a percentage down block
 * @param block - The percentage down block to execute
 * @param polygonData - Data from Polygon API
 * @returns Boolean indicating if the percentage threshold was met
 */
function executePercentageDownBlock(block: any, polygonData: PolygonData): boolean {
  const { percentage } = block;

  // Get current price and calculate previous close from historical data
  const currentPrice = polygonData.price.current || polygonData.price.close;
  const historicalCloses = polygonData.historical.close;

  if (!currentPrice || !historicalCloses || historicalCloses.length < 2) {
    console.warn('Missing price data for percentage down block');
    return false;
  }

  // Use the second-to-last close as previous close (last close might be current)
  const previousClose = historicalCloses[historicalCloses.length - 2];

  if (!previousClose) {
    console.warn('Missing previous close data for percentage down block');
    return false;
  }

  // Calculate percentage change
  const percentageChange = ((currentPrice - previousClose) / previousClose) * 100;

  console.log(`Percentage Down Block: Current: ${currentPrice}, Previous: ${previousClose}, Change: ${percentageChange.toFixed(2)}%, Threshold: -${percentage}%`);

  // Return true if the percentage change meets or exceeds the threshold (negative)
  return percentageChange <= -percentage;
}

/**
 * Execute an agent based on its configuration
 * @param agentConfig - The agent configuration
 * @param polygonData - Data from Polygon API
 * @returns The result of executing the agent
 */
export function executeAgent(
  agentConfig: AgentConfig,
  polygonData: PolygonData
): AgentResult {
  const startTime = Date.now();
  const blockResults = new Map<string, any>();
  const executionPath: string[] = [];

  // Create a map of blocks by ID for easy lookup
  const blocksById = new Map<string, BlockUnion>();
  for (const block of agentConfig.blocks) {
    blocksById.set(block.id, block);
  }

  // Start with the entry block
  let currentBlockId: string | null = agentConfig.entryBlockId;
  let inheritedValue: any = undefined; // Track value passed through condition chains
  let result: AgentResult = {
    signal: 'neutral',
    confidence: 0,
    reasoning: '',
    metrics: {},
    executionPath: [],
    executionTime: 0,
    timestamp: new Date().toISOString()
  };

  // Execute blocks in sequence
  while (currentBlockId) {
    const currentBlock = blocksById.get(currentBlockId);
    if (!currentBlock) {
      throw new Error(`Block not found: ${currentBlockId}`);
    }

    console.log(`\n=== Executing Block: ${currentBlock.type} (ID: ${currentBlockId}) ===`);

    // Execute the block with inherited value if applicable
    const blockResult = executeBlock(currentBlock, blockResults, polygonData, inheritedValue);
    blockResults.set(currentBlockId, blockResult);
    executionPath.push(currentBlockId);

    console.log(`Block result: ${JSON.stringify(blockResult)}`);

    // If this is a trigger block, set the result
    if (currentBlock.type === BlockType.TRIGGER) {
      console.log(`Setting final agent result from trigger: signal=${blockResult.signal}, confidence=${blockResult.confidence}`);
      result.signal = blockResult.signal;
      result.confidence = blockResult.confidence;
    }

    // Determine the next block to execute and manage inherited values
    if (currentBlock.type === BlockType.CONDITION) {
      // For condition blocks, follow the true or false path
      let nextBlockId: string | null;
      if (blockResult) {
        // Condition is true - follow true path if it exists, otherwise stop execution
        nextBlockId = currentBlock.trueConnection || null;
      } else {
        // Condition is false - follow false path if it exists, otherwise stop execution
        nextBlockId = currentBlock.falseConnection || null;
      }

      // Check if the next block is also a condition block
      const nextBlock = nextBlockId ? blocksById.get(nextBlockId) : null;
      if (nextBlock && nextBlock.type === BlockType.CONDITION) {
        // If the next block is a condition, pass the original input value
        // Get the original input value that was used in this condition
        if (inheritedValue !== undefined) {
          // Keep passing the inherited value
          inheritedValue = inheritedValue;
        } else {
          // Get the input value from this condition block's input connections
          const inputConnections = currentBlock.inputConnections || [];
          if (inputConnections.length > 0) {
            const inputId = inputConnections[0];
            inheritedValue = blockResults.get(inputId);
          }
        }
        console.log(`Passing inherited value ${inheritedValue} to next condition block ${nextBlockId}`);
      } else {
        // Reset inherited value if next block is not a condition
        inheritedValue = undefined;
      }

      currentBlockId = nextBlockId;
    } else if (
      currentBlock.type === BlockType.WHEN_RUN ||
      currentBlock.type === BlockType.INDICATOR ||
      currentBlock.type === BlockType.PRICE ||
      currentBlock.type === BlockType.FUNDAMENTAL ||
      currentBlock.type === BlockType.OPERATOR ||
      currentBlock.type === BlockType.PERCENTAGE_UP ||
      currentBlock.type === BlockType.PERCENTAGE_DOWN
    ) {
      // For blocks with multiple output connections, we need to execute ALL paths
      const outputConnections = currentBlock.outputConnections || [];

      if (outputConnections.length > 1) {
        console.log(`Block has ${outputConnections.length} output connections, executing all paths`);

        // Execute all remaining paths recursively
        for (let i = 1; i < outputConnections.length; i++) {
          const pathResult = executePathFromBlock(
            outputConnections[i],
            blocksById,
            new Map(blockResults), // Copy current results
            [...executionPath], // Copy current path
            polygonData
          );

          // If this path resulted in a trigger, update our result
          if (pathResult.signal !== 'neutral') {
            console.log(`Path ${i} resulted in signal: ${pathResult.signal} with confidence ${pathResult.confidence}`);
            result.signal = pathResult.signal;
            result.confidence = pathResult.confidence;
          }
        }

        // Continue with the first path in the main loop
        currentBlockId = outputConnections[0] || null;
      } else {
        // Single output connection, follow it normally
        currentBlockId = outputConnections[0] || null;
      }

      // Reset inherited value when moving to non-condition blocks
      inheritedValue = undefined;
    } else {
      // For trigger blocks or blocks without connections, stop execution
      currentBlockId = null;
      inheritedValue = undefined;
    }
  }

  // Calculate execution time
  const executionTime = Date.now() - startTime;

  // Generate reasoning based on the execution path
  const reasoning = generateReasoning(agentConfig, blocksById, blockResults, executionPath);

  // Collect metrics from the execution
  const metrics = collectMetrics(blocksById, blockResults, executionPath);

  // Create the final result with all required fields
  const finalResult = {
    signal: result.signal || 'neutral',
    confidence: typeof result.confidence === 'number' ? result.confidence : 0,
    reasoning: reasoning || '',
    metrics: metrics || {},
    executionPath: executionPath || [],
    executionTime: executionTime || 0,
    timestamp: new Date().toISOString()
  };

  // Log the result for debugging
  console.log(`Agent execution completed with signal: ${finalResult.signal}, confidence: ${finalResult.confidence}`);
  console.log(`Agent execution result: signal=${finalResult.signal}, confidence=${finalResult.confidence}`);

  return finalResult;
}

/**
 * Execute a path starting from a specific block
 * @param startBlockId - The block ID to start execution from
 * @param blocksById - Map of blocks by ID
 * @param blockResults - Results of previously executed blocks
 * @param executionPath - Path of executed blocks
 * @param polygonData - Data from Polygon API
 * @returns The result of executing the path
 */
function executePathFromBlock(
  startBlockId: string | null,
  blocksById: Map<string, BlockUnion>,
  blockResults: Map<string, any>,
  executionPath: string[],
  polygonData: PolygonData
): AgentResult {
  let currentBlockId = startBlockId;
  let inheritedValue: any = undefined;
  let result: AgentResult = {
    signal: 'neutral',
    confidence: 0,
    reasoning: '',
    metrics: {},
    executionPath: [],
    executionTime: 0,
    timestamp: new Date().toISOString()
  };

  // Execute blocks in this path
  while (currentBlockId) {
    const currentBlock = blocksById.get(currentBlockId);
    if (!currentBlock) {
      console.error(`Block not found in path execution: ${currentBlockId}`);
      break;
    }

    console.log(`Path execution: ${currentBlock.type} (ID: ${currentBlockId})`);

    // Execute the block if not already executed
    let blockResult: any;
    if (blockResults.has(currentBlockId)) {
      blockResult = blockResults.get(currentBlockId);
    } else {
      blockResult = executeBlock(currentBlock, blockResults, polygonData, inheritedValue);
      blockResults.set(currentBlockId, blockResult);
      executionPath.push(currentBlockId);
    }

    // If this is a trigger block, set the result and stop
    if (currentBlock.type === BlockType.TRIGGER) {
      console.log(`Path reached trigger: signal=${blockResult.signal}, confidence=${blockResult.confidence}`);
      result.signal = blockResult.signal;
      result.confidence = blockResult.confidence;
      break;
    }

    // Determine the next block
    if (currentBlock.type === BlockType.CONDITION) {
      if (blockResult) {
        currentBlockId = currentBlock.trueConnection || null;
      } else {
        currentBlockId = currentBlock.falseConnection || null;
      }

      // Handle inherited values for condition chains
      const nextBlock = currentBlockId ? blocksById.get(currentBlockId) : null;
      if (nextBlock && nextBlock.type === BlockType.CONDITION) {
        if (inheritedValue === undefined) {
          const inputConnections = currentBlock.inputConnections || [];
          if (inputConnections.length > 0) {
            inheritedValue = blockResults.get(inputConnections[0]);
          }
        }
      } else {
        inheritedValue = undefined;
      }
    } else {
      // For other block types, follow the first output connection
      currentBlockId = currentBlock.outputConnections?.[0] || null;
      inheritedValue = undefined;
    }
  }

  return result;
}

/**
 * Generate reasoning for the agent's decision
 * @param agentConfig - The agent configuration
 * @param blocksById - Map of blocks by ID
 * @param blockResults - Results of executed blocks
 * @param executionPath - Order of block execution
 * @returns Reasoning string
 */
function generateReasoning(
  agentConfig: AgentConfig,
  blocksById: Map<string, BlockUnion>,
  blockResults: Map<string, any>,
  executionPath: string[]
): string {
  // Use a default name if agentConfig.name is undefined
  const agentName = agentConfig.name || 'Agent';
  let reasoning = `${agentName} analyzed the data and `;

  // Find the trigger block (if any)
  const triggerBlock = Array.from(blocksById.values())
    .find(block => block.type === BlockType.TRIGGER);

  if (triggerBlock && blockResults.has(triggerBlock.id)) {
    const triggerResult = blockResults.get(triggerBlock.id);

    if (triggerResult.signal === 'neutral') {
      reasoning += 'found no clear signal. ';
    } else {
      reasoning += `generated a ${triggerResult.signal.toUpperCase()} signal with ${triggerResult.confidence}% confidence. `;
    }
  } else {
    reasoning += 'did not reach a conclusion. ';
  }

  // Add details about key indicators and conditions
  reasoning += 'The analysis considered: ';

  const indicatorBlocks = executionPath
    .filter(id => blocksById.get(id)?.type === BlockType.INDICATOR)
    .map(id => {
      const block = blocksById.get(id);
      const result = blockResults.get(id);
      if (!block || block.type !== BlockType.INDICATOR) return '';
      return `${(block as any).indicator} (${typeof result === 'object' ? JSON.stringify(result) : result})`;
    })
    .filter(text => text !== '');

  if (indicatorBlocks.length > 0) {
    reasoning += `Indicators: ${indicatorBlocks.join(', ')}. `;
  }

  const priceBlocks = executionPath
    .filter(id => blocksById.get(id)?.type === BlockType.PRICE)
    .map(id => {
      const block = blocksById.get(id);
      const result = blockResults.get(id);
      if (!block || block.type !== BlockType.PRICE) return '';
      return `${(block as any).dataPoint} price (${result})`;
    })
    .filter(text => text !== '');

  if (priceBlocks.length > 0) {
    reasoning += `Price data: ${priceBlocks.join(', ')}. `;
  }

  const fundamentalBlocks = executionPath
    .filter(id => blocksById.get(id)?.type === BlockType.FUNDAMENTAL)
    .map(id => {
      const block = blocksById.get(id);
      const result = blockResults.get(id);

      if (!block || block.type !== BlockType.FUNDAMENTAL) return '';
      const fundamentalBlock = block as any;

      // Handle calculated metrics with components
      if (result && typeof result === 'object' && result.value !== undefined && result.components) {
        const components = result.components;
        if (fundamentalBlock.metric === 'return_on_equity') {
          const netIncome = components.net_income_ttm || components.net_income;
          const equity = components.shareholders_equity;
          if (netIncome && equity) {
            return `ROE: ${result.value.toFixed(2)}% (Net Income TTM: $${netIncome.toLocaleString()}, Equity: $${equity.toLocaleString()})`;
          } else {
            return `ROE: ${result.value.toFixed(2)}% (calculated)`;
          }
        }
        return `${fundamentalBlock.metric}: ${result.value} (calculated)`;
      }

      return `${fundamentalBlock.metric} (${result})`;
    })
    .filter(text => text !== '');

  if (fundamentalBlocks.length > 0) {
    reasoning += `Fundamentals: ${fundamentalBlocks.join(', ')}. `;
  }

  return reasoning;
}

/**
 * Collect metrics from the execution
 * @param blocksById - Map of blocks by ID
 * @param blockResults - Results of executed blocks
 * @param executionPath - Order of block execution
 * @returns Object with metrics
 */
function collectMetrics(
  blocksById: Map<string, BlockUnion>,
  blockResults: Map<string, any>,
  executionPath: string[]
): Record<string, any> {
  const metrics: Record<string, any> = {};

  // Collect indicator values
  const indicatorMetrics: Record<string, any> = {};
  for (const id of executionPath) {
    const block = blocksById.get(id);
    if (block?.type === BlockType.INDICATOR) {
      indicatorMetrics[block.indicator] = blockResults.get(id);
    }
  }

  if (Object.keys(indicatorMetrics).length > 0) {
    metrics.indicators = indicatorMetrics;
  }

  // Collect price values
  const priceMetrics: Record<string, any> = {};
  for (const id of executionPath) {
    const block = blocksById.get(id);
    if (block?.type === BlockType.PRICE) {
      priceMetrics[block.dataPoint] = blockResults.get(id);
    }
  }

  if (Object.keys(priceMetrics).length > 0) {
    metrics.prices = priceMetrics;
  }

  // Collect fundamental values
  const fundamentalMetrics: Record<string, any> = {};
  for (const id of executionPath) {
    const block = blocksById.get(id);
    if (block?.type === BlockType.FUNDAMENTAL) {
      fundamentalMetrics[block.metric] = blockResults.get(id);
    }
  }

  if (Object.keys(fundamentalMetrics).length > 0) {
    metrics.fundamentals = fundamentalMetrics;
  }

  return metrics;
}

