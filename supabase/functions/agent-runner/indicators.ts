// Technical Indicators
// Functions to calculate technical indicators

/**
 * Calculate Simple Moving Average (SMA)
 * @param data - Array of price data
 * @param period - Period for the SMA
 * @returns Array of SMA values
 */
function calculateSMA(data: number[], period: number): number[] {
  const result: number[] = [];

  for (let i = 0; i < data.length; i++) {
    if (i < period - 1) {
      result.push(NaN); // Not enough data for the period
      continue;
    }

    let sum = 0;
    for (let j = 0; j < period; j++) {
      sum += data[i - j];
    }

    result.push(sum / period);
  }

  return result;
}

/**
 * Calculate Exponential Moving Average (EMA)
 * @param data - Array of price data
 * @param period - Period for the EMA
 * @returns Array of EMA values
 */
function calculateEMA(data: number[], period: number): number[] {
  const result: number[] = [];
  const multiplier = 2 / (period + 1);

  // Start with SMA for the first value
  let ema = data.slice(0, period).reduce((sum, price) => sum + price, 0) / period;

  for (let i = 0; i < data.length; i++) {
    if (i < period - 1) {
      result.push(NaN); // Not enough data for the period
      continue;
    }

    if (i === period - 1) {
      result.push(ema);
      continue;
    }

    // EMA = (Close - Previous EMA) * multiplier + Previous EMA
    ema = (data[i] - ema) * multiplier + ema;
    result.push(ema);
  }

  return result;
}

/**
 * Calculate Relative Strength Index (RSI)
 * @param data - Array of price data
 * @param period - Period for the RSI (typically 14)
 * @returns Array of RSI values
 */
export function calculateRSI(data: number[], period: number = 14): number[] {
  const result: number[] = [];
  const gains: number[] = [];
  const losses: number[] = [];

  // Calculate price changes
  for (let i = 1; i < data.length; i++) {
    const change = data[i] - data[i - 1];
    gains.push(change > 0 ? change : 0);
    losses.push(change < 0 ? Math.abs(change) : 0);
  }

  // Calculate average gains and losses
  for (let i = 0; i < data.length; i++) {
    if (i < period) {
      result.push(NaN); // Not enough data for the period
      continue;
    }

    if (i === period) {
      // First RSI value uses simple average
      const avgGain = gains.slice(0, period).reduce((sum, gain) => sum + gain, 0) / period;
      const avgLoss = losses.slice(0, period).reduce((sum, loss) => sum + loss, 0) / period;

      if (avgLoss === 0) {
        result.push(100); // No losses, RSI is 100
      } else {
        const rs = avgGain / avgLoss;
        result.push(100 - (100 / (1 + rs)));
      }

      continue;
    }

    // Subsequent values use smoothed average
    const prevAvgGain = (result[i - 1] === 100 ? 1 : (result[i - 1] / (100 - result[i - 1]))) * (period - 1);
    const avgGain = (prevAvgGain * (period - 1) + gains[i - 1]) / period;

    const prevAvgLoss = (result[i - 1] === 0 ? 1 : ((100 - result[i - 1]) / result[i - 1])) * (period - 1);
    const avgLoss = (prevAvgLoss * (period - 1) + losses[i - 1]) / period;

    if (avgLoss === 0) {
      result.push(100); // No losses, RSI is 100
    } else {
      const rs = avgGain / avgLoss;
      result.push(100 - (100 / (1 + rs)));
    }
  }

  return result;
}

/**
 * Calculate Moving Average Convergence Divergence (MACD)
 * @param data - Array of price data
 * @param fastPeriod - Fast EMA period (typically 12)
 * @param slowPeriod - Slow EMA period (typically 26)
 * @param signalPeriod - Signal EMA period (typically 9)
 * @returns Object with MACD line, signal line, and histogram
 */
function calculateMACD(
  data: number[],
  fastPeriod: number = 12,
  slowPeriod: number = 26,
  signalPeriod: number = 9
): { macd: number[], signal: number[], histogram: number[] } {
  const fastEMA = calculateEMA(data, fastPeriod);
  const slowEMA = calculateEMA(data, slowPeriod);

  // Calculate MACD line (fast EMA - slow EMA)
  const macdLine: number[] = [];
  for (let i = 0; i < data.length; i++) {
    if (isNaN(fastEMA[i]) || isNaN(slowEMA[i])) {
      macdLine.push(NaN);
    } else {
      macdLine.push(fastEMA[i] - slowEMA[i]);
    }
  }

  // Calculate signal line (EMA of MACD line)
  const signalLine = calculateEMA(
    macdLine.filter(val => !isNaN(val)),
    signalPeriod
  );

  // Pad signal line with NaN values to match the length of the MACD line
  const paddedSignalLine: number[] = Array(macdLine.length).fill(NaN);
  const validMacdCount = macdLine.filter(val => !isNaN(val)).length;
  const signalStartIndex = macdLine.length - validMacdCount + signalPeriod - 1;

  for (let i = 0; i < signalLine.length; i++) {
    paddedSignalLine[signalStartIndex + i] = signalLine[i];
  }

  // Calculate histogram (MACD line - signal line)
  const histogram: number[] = [];
  for (let i = 0; i < macdLine.length; i++) {
    if (isNaN(macdLine[i]) || isNaN(paddedSignalLine[i])) {
      histogram.push(NaN);
    } else {
      histogram.push(macdLine[i] - paddedSignalLine[i]);
    }
  }

  return {
    macd: macdLine,
    signal: paddedSignalLine,
    histogram
  };
}

/**
 * Calculate Bollinger Bands
 * @param data - Array of price data
 * @param period - Period for the SMA (typically 20)
 * @param multiplier - Standard deviation multiplier (typically 2)
 * @returns Object with upper band, middle band (SMA), and lower band
 */
function calculateBollingerBands(
  data: number[],
  period: number = 20,
  multiplier: number = 2
): { upper: number[], middle: number[], lower: number[] } {
  const middle = calculateSMA(data, period);
  const upper: number[] = [];
  const lower: number[] = [];

  for (let i = 0; i < data.length; i++) {
    if (i < period - 1) {
      upper.push(NaN);
      lower.push(NaN);
      continue;
    }

    // Calculate standard deviation
    let sum = 0;
    for (let j = 0; j < period; j++) {
      sum += Math.pow(data[i - j] - middle[i], 2);
    }
    const stdDev = Math.sqrt(sum / period);

    upper.push(middle[i] + multiplier * stdDev);
    lower.push(middle[i] - multiplier * stdDev);
  }

  return { upper, middle, lower };
}

/**
 * Calculate Average True Range (ATR)
 * @param high - Array of high prices
 * @param low - Array of low prices
 * @param close - Array of close prices
 * @param period - Period for the ATR (typically 14)
 * @returns Array of ATR values
 */
function calculateATR(
  high: number[],
  low: number[],
  close: number[],
  period: number = 14
): number[] {
  const trueRanges: number[] = [];
  const result: number[] = [];

  // Calculate true ranges
  for (let i = 0; i < high.length; i++) {
    if (i === 0) {
      trueRanges.push(high[i] - low[i]); // First TR is simply the high - low
    } else {
      // TR = max(high - low, abs(high - prevClose), abs(low - prevClose))
      const tr1 = high[i] - low[i];
      const tr2 = Math.abs(high[i] - close[i - 1]);
      const tr3 = Math.abs(low[i] - close[i - 1]);
      trueRanges.push(Math.max(tr1, tr2, tr3));
    }
  }

  // Calculate ATR
  for (let i = 0; i < high.length; i++) {
    if (i < period - 1) {
      result.push(NaN); // Not enough data for the period
      continue;
    }

    if (i === period - 1) {
      // First ATR is simple average of TR values
      const sum = trueRanges.slice(0, period).reduce((acc, tr) => acc + tr, 0);
      result.push(sum / period);
      continue;
    }

    // Subsequent ATR values use smoothed average
    // ATR = ((Previous ATR * (period - 1)) + Current TR) / period
    result.push((result[i - 1] * (period - 1) + trueRanges[i]) / period);
  }

  return result;
}

/**
 * Calculate Stochastic Oscillator
 * @param high - Array of high prices
 * @param low - Array of low prices
 * @param close - Array of close prices
 * @param kPeriod - %K period (typically 14)
 * @param dPeriod - %D period (typically 3)
 * @returns Object with %K and %D values
 */
function calculateStochastic(
  high: number[],
  low: number[],
  close: number[],
  kPeriod: number = 14,
  dPeriod: number = 3
): { k: number[], d: number[] } {
  const k: number[] = [];

  // Calculate %K
  for (let i = 0; i < close.length; i++) {
    if (i < kPeriod - 1) {
      k.push(NaN); // Not enough data for the period
      continue;
    }

    // Find highest high and lowest low in the period
    let highestHigh = -Infinity;
    let lowestLow = Infinity;

    for (let j = 0; j < kPeriod; j++) {
      highestHigh = Math.max(highestHigh, high[i - j]);
      lowestLow = Math.min(lowestLow, low[i - j]);
    }

    // %K = ((Close - Lowest Low) / (Highest High - Lowest Low)) * 100
    if (highestHigh === lowestLow) {
      k.push(100); // Avoid division by zero
    } else {
      k.push(((close[i] - lowestLow) / (highestHigh - lowestLow)) * 100);
    }
  }

  // Calculate %D (SMA of %K)
  const d = calculateSMA(k.filter(val => !isNaN(val)), dPeriod);

  // Pad %D with NaN values to match the length of %K
  const paddedD: number[] = Array(k.length).fill(NaN);
  const validKCount = k.filter(val => !isNaN(val)).length;
  const dStartIndex = k.length - validKCount + dPeriod - 1;

  for (let i = 0; i < d.length; i++) {
    paddedD[dStartIndex + i] = d[i];
  }

  return { k, d: paddedD };
}

/**
 * Calculate all required indicators
 * @param historicalData - Historical price data
 * @param indicators - Array of indicator names to calculate
 * @returns Object with calculated indicators
 */
export function calculateIndicators(
  historicalData: {
    open: number[];
    high: number[];
    low: number[];
    close: number[];
    volume: number[];
    timestamp: number[];
  },
  indicators: string[]
): Record<string, any> {
  const result: Record<string, any> = {};

  console.log(`Calculating indicators: ${indicators.join(', ')}`);

  // Check if we have enough data to calculate indicators
  if (!historicalData.close || historicalData.close.length === 0) {
    console.error('No historical close data available for indicator calculation');
    return result;
  }

  console.log(`Historical data available: ${historicalData.close.length} data points`);

  for (const indicator of indicators) {
    const normalizedIndicator = indicator.toLowerCase().trim();
    console.log(`Processing indicator: ${normalizedIndicator}`);

    switch (normalizedIndicator) {
      case 'sma':
        result.sma = {
          sma20: calculateSMA(historicalData.close, 20),
          sma50: calculateSMA(historicalData.close, 50),
          sma200: calculateSMA(historicalData.close, 200)
        };
        break;
      case 'ema':
        result.ema = {
          ema12: calculateEMA(historicalData.close, 12),
          ema26: calculateEMA(historicalData.close, 26),
          ema50: calculateEMA(historicalData.close, 50),
          ema200: calculateEMA(historicalData.close, 200)
        };
        break;
      case 'rsi':
        result.rsi = calculateRSI(historicalData.close);
        break;
      case 'macd':
        result.macd = calculateMACD(historicalData.close);
        break;
      case 'bollinger':
        result.bollinger = calculateBollingerBands(historicalData.close);
        break;
      case 'atr':
        result.atr = calculateATR(
          historicalData.high,
          historicalData.low,
          historicalData.close
        );
        break;
      case 'stochastic':
        result.stochastic = calculateStochastic(
          historicalData.high,
          historicalData.low,
          historicalData.close
        );
        break;
      // Add more indicators as needed
    }
  }

  return result;
}
