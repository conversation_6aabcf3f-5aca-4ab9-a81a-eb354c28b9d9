// Agent Types
// Type definitions for the agent system

// Block types
export enum BlockType {
  WHEN_RUN = 'WHEN_RUN', // Added WHEN_RUN block type
  INDICATOR = 'INDICATOR',
  PRICE = 'PRICE',
  FUNDAMENTAL = 'FUNDAMENTAL',
  CONDITION = 'CONDITION',
  TRIGGER = 'TRIGGER',
  OPERATOR = 'OPERATOR',
  PERCENTAGE_UP = 'PERCENTAGE_UP',
  PERCENTAGE_DOWN = 'PERCENTAGE_DOWN'
}

// Base block interface
export interface Block {
  id: string;
  type: BlockType;
  position: { x: number; y: number };
}

// Technical indicator block
export interface IndicatorBlock extends Block {
  type: BlockType.INDICATOR;
  indicator: string; // 'rsi', 'macd', 'sma', etc.
  parameters: Record<string, any>; // Parameters specific to the indicator
  outputConnections: string[]; // IDs of connected blocks
}

// Price data block
export interface PriceBlock extends Block {
  type: BlockType.PRICE;
  dataPoint: string; // 'open', 'high', 'low', 'close', 'volume', etc.
  timeframe?: string; // Optional timeframe override
  lookback?: number; // How many periods to look back
  outputConnections: string[]; // IDs of connected blocks
}

// Fundamental data block
export interface FundamentalBlock extends Block {
  type: BlockType.FUNDAMENTAL;
  metric: string; // The specific financial metric to retrieve
  statement: 'balance_sheet' | 'income_statement' | 'cash_flow_statement' | 'comprehensive_income'; // Which financial statement
  period?: 'quarterly' | 'annual'; // Time period (defaults to most recent)
  parameters?: Record<string, any>; // Additional parameters
  outputConnections: string[]; // IDs of connected blocks
}

// Condition block (if/then/else)
export interface ConditionBlock extends Block {
  type: BlockType.CONDITION;
  operator: string; // '>', '<', '==', '>=', '<=', '!=', 'between', 'and', 'or', 'not'
  inputConnections: string[]; // IDs of input blocks
  compareValue?: number; // Value to compare against (not used for 'and', 'or', 'not')
  trueConnection?: string; // ID of block to execute if condition is true
  falseConnection?: string; // ID of block to execute if condition is false
}

// Trigger block (final output)
export interface TriggerBlock extends Block {
  type: BlockType.TRIGGER;
  signal: 'bullish' | 'bearish' | 'neutral';
  confidence: number; // 0-100
  inputConnections: string[]; // IDs of input blocks
}

// When Run block (entry point)
export interface WhenRunBlock extends Block {
  type: BlockType.WHEN_RUN;
  outputConnections: string[]; // IDs of connected blocks
}

// Operator block (mathematical operations)
export interface OperatorBlock extends Block {
  type: BlockType.OPERATOR;
  operation: string; // 'add', 'subtract', 'multiply', 'divide', 'average', etc.
  inputConnections: string[]; // IDs of input blocks
  outputConnections: string[]; // IDs of connected blocks
}

// Percentage Up block (action block for percentage gains)
export interface PercentageUpBlock extends Block {
  type: BlockType.PERCENTAGE_UP;
  percentage: number; // Percentage threshold (e.g., 5 for +5%)
  inputConnections: string[]; // IDs of input blocks
  outputConnections: string[]; // IDs of connected blocks (non-terminal)
}

// Percentage Down block (action block for percentage losses)
export interface PercentageDownBlock extends Block {
  type: BlockType.PERCENTAGE_DOWN;
  percentage: number; // Percentage threshold (e.g., 10 for -10%)
  inputConnections: string[]; // IDs of input blocks
  outputConnections: string[]; // IDs of connected blocks (non-terminal)
}

// Union type for all block types
export type BlockUnion =
  | WhenRunBlock
  | IndicatorBlock
  | PriceBlock
  | FundamentalBlock
  | ConditionBlock
  | TriggerBlock
  | OperatorBlock
  | PercentageUpBlock
  | PercentageDownBlock;

// Agent configuration
export interface AgentConfig {
  name?: string; // Optional name for the agent
  description?: string;
  blocks: BlockUnion[];
  entryBlockId: string; // ID of the first block to execute
}

// Agent execution result
export interface AgentResult {
  signal: 'bullish' | 'bearish' | 'neutral';
  confidence: number; // 0-100
  reasoning: string;
  metrics: Record<string, any>; // Metrics calculated during execution
  executionPath: string[]; // IDs of blocks executed in order
  executionTime: number; // Time taken to execute in ms
  timestamp: string; // ISO timestamp of execution
}

// Financial statement data structure
export interface FinancialStatement {
  [metric: string]: {
    label: string;
    order: number;
    unit: string;
    value: number;
  } | number;
}

// Complete financials data structure
export interface FinancialsData {
  balance_sheet?: FinancialStatement;
  income_statement?: FinancialStatement;
  cash_flow_statement?: FinancialStatement;
  comprehensive_income?: FinancialStatement;
}

// Polygon data structure
export interface PolygonData {
  price: {
    current: number;
    open: number;
    high: number;
    low: number;
    close: number;
    volume: number;
    timestamp: number;
  };
  historical: {
    open: number[];
    high: number[];
    low: number[];
    close: number[];
    volume: number[];
    timestamp: number[];
  };
  indicators: Record<string, any>; // Calculated indicators
  fundamentals: Record<string, any>; // Legacy fundamental data
  financials?: FinancialsData; // Complete financial statements data
}
