import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';
import { BarChart2, TrendingUp, TrendingDown, DollarSign, Calculator, GitBranch, Target, Zap } from 'lucide-react';
import { BlockType } from '@/hooks/useAgentBuilder';

interface BlockPaletteProps {
  onBlockDrop: (type: string, position: { x: number; y: number }) => void;
}

interface BlockItemProps {
  type: BlockType;
  title: string;
  icon: React.ReactNode;
  description: string;
  onBlockDrop: (type: string, position: { x: number; y: number }) => void;
  properties?: Record<string, any>;
}

const BlockItem: React.FC<BlockItemProps> = ({ type, title, icon, description, onBlockDrop, properties }) => {
  const handleDragStart = (e: React.DragEvent) => {
    // Store both the type and any additional properties
    const dragData = {
      type,
      properties: properties || {}
    };

    // Ensure position is not included in the properties to avoid issues
    if (dragData.properties.position) {
      delete dragData.properties.position;
    }

    e.dataTransfer.setData('application/reactflow', JSON.stringify(dragData));
    e.dataTransfer.effectAllowed = 'move';

    // Create a custom drag image to prevent showing the entire sidebar
    const dragImage = document.createElement('div');
    dragImage.style.cssText = `
      position: absolute;
      top: -1000px;
      left: -1000px;
      width: 200px;
      height: 60px;
      background: linear-gradient(135deg, rgba(26, 26, 26, 0.95) 0%, rgba(20, 20, 20, 0.9) 100%);
      border: 1px solid rgba(255, 255, 255, 0.1);
      border-radius: 12px;
      padding: 12px;
      color: white;
      font-size: 12px;
      font-weight: 500;
      display: flex;
      align-items: center;
      gap: 8px;
      backdrop-filter: blur(8px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    `;
    dragImage.innerHTML = `
      <div style="width: 24px; height: 24px; background: rgba(255, 255, 255, 0.1); border-radius: 6px; display: flex; align-items: center; justify-content: center;">
        ${icon ? icon.toString() : '📦'}
      </div>
      <span>${title}</span>
    `;

    document.body.appendChild(dragImage);
    e.dataTransfer.setDragImage(dragImage, 100, 30);

    // Clean up the drag image immediately
    setTimeout(() => {
      if (document.body.contains(dragImage)) {
        document.body.removeChild(dragImage);
      }
    }, 0);
  };

  return (
    <div
      className="mb-3 cursor-grab select-none"
      draggable
      onDragStart={handleDragStart}
    >
      <div className="relative rounded-lg border border-white/[0.04] bg-[#1A1A1A]/60">
        <div className="relative p-3 flex items-center gap-3">
          {/* Icon container */}
          <div className="flex-shrink-0 w-8 h-8 flex items-center justify-center rounded-md bg-white/[0.03] border border-white/[0.05]">
            <div className="text-white/70">
              {icon}
            </div>
          </div>

          {/* Content */}
          <div className="flex-1 min-w-0">
            <h3 className="text-sm font-medium text-white/85 truncate">
              {title}
            </h3>
            <p className="text-xs text-white/50 line-clamp-2 mt-0.5">
              {description}
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

const BlockPalette: React.FC<BlockPaletteProps> = ({ onBlockDrop }) => {
  return (
    <div className="space-y-6">
      <Accordion type="multiple" defaultValue={['data', 'logic', 'actions', 'output']} className="space-y-4">
        <AccordionItem value="data" className="border-none">
          <AccordionTrigger className="py-4 px-0 text-sm font-medium text-white/70 hover:no-underline border-b border-white/[0.03] tracking-wide">
            <div className="flex items-center gap-3">
              <div className="w-1.5 h-1.5 rounded-full bg-emerald-400/80" />
              <span className="uppercase text-xs letter-spacing-wider">Data Sources</span>
            </div>
          </AccordionTrigger>
          <AccordionContent className="pt-4 pb-2">
          <BlockItem
            type={BlockType.INDICATOR}
            title="Technical Indicator"
            icon={<BarChart2 className="h-4 w-4" />}
            description="RSI, MACD, SMA, EMA, Bollinger Bands, Support/Resistance, Candle Patterns, etc."
            onBlockDrop={onBlockDrop}
            properties={{ indicator: 'rsi', parameters: { period: 14 } }}
          />
          <BlockItem
            type={BlockType.PRICE}
            title="Price Data"
            icon={<DollarSign className="h-4 w-4" />}
            description="Open, High, Low, Close, Volume"
            onBlockDrop={onBlockDrop}
            properties={{ dataPoint: 'close', lookback: 0 }}
          />
          <BlockItem
            type={BlockType.FUNDAMENTAL}
            title="Fundamental Data"
            icon={<TrendingUp className="h-4 w-4" />}
            description="P/E, P/B, ROE, Revenue Growth, etc."
            onBlockDrop={onBlockDrop}
            properties={{ metric: 'return_on_equity', statement: 'calculated', period: 'quarterly' }}
          />
          </AccordionContent>
        </AccordionItem>

        <AccordionItem value="logic" className="border-none">
          <AccordionTrigger className="py-4 px-0 text-sm font-medium text-white/70 hover:no-underline border-b border-white/[0.03] tracking-wide">
            <div className="flex items-center gap-3">
              <div className="w-1.5 h-1.5 rounded-full bg-emerald-400/80" />
              <span className="uppercase text-xs letter-spacing-wider">Logic & Processing</span>
            </div>
          </AccordionTrigger>
          <AccordionContent className="pt-4 pb-2">
          <BlockItem
            type={BlockType.CONDITION}
            title="Condition"
            icon={<GitBranch className="h-4 w-4" />}
            description=">, <, ==, >=, <=, !=, AND, OR, etc."
            onBlockDrop={onBlockDrop}
            properties={{ operator: '>', compareValue: 0 }}
          />
          <BlockItem
            type={BlockType.OPERATOR}
            title="Math Operator"
            icon={<Calculator className="h-4 w-4" />}
            description="Add, Subtract, Multiply, Divide, Average, etc."
            onBlockDrop={onBlockDrop}
            properties={{ operation: 'add' }}
          />
          </AccordionContent>
        </AccordionItem>

        <AccordionItem value="actions" className="border-none">
          <AccordionTrigger className="py-4 px-0 text-sm font-medium text-white/70 hover:no-underline border-b border-white/[0.03] tracking-wide">
            <div className="flex items-center gap-3">
              <div className="w-1.5 h-1.5 rounded-full bg-emerald-400/80" />
              <span className="uppercase text-xs letter-spacing-wider">Actions</span>
            </div>
          </AccordionTrigger>
          <AccordionContent className="pt-4 pb-2">
          <BlockItem
            type={BlockType.PERCENTAGE_UP}
            title="Percentage Up"
            icon={<TrendingUp className="h-4 w-4" />}
            description="Triggers when price moves up by specified percentage"
            onBlockDrop={onBlockDrop}
            properties={{ percentage: 5.0 }}
          />
          <BlockItem
            type={BlockType.PERCENTAGE_DOWN}
            title="Percentage Down"
            icon={<TrendingDown className="h-4 w-4" />}
            description="Triggers when price moves down by specified percentage"
            onBlockDrop={onBlockDrop}
            properties={{ percentage: 10.0 }}
          />
          </AccordionContent>
        </AccordionItem>

        <AccordionItem value="output" className="border-none">
          <AccordionTrigger className="py-4 px-0 text-sm font-medium text-white/70 hover:no-underline border-b border-white/[0.03] tracking-wide">
            <div className="flex items-center gap-3">
              <div className="w-1.5 h-1.5 rounded-full bg-emerald-400/80" />
              <span className="uppercase text-xs letter-spacing-wider">Output Signals</span>
            </div>
          </AccordionTrigger>
          <AccordionContent className="pt-4 pb-2">
          <BlockItem
            type={BlockType.TRIGGER}
            title="Signal"
            icon={<Target className="h-4 w-4" />}
            description="Bullish, Bearish, or Neutral signal"
            onBlockDrop={onBlockDrop}
            properties={{ signal: 'bullish', confidence: 75 }}
          />
          </AccordionContent>
        </AccordionItem>
      </Accordion>
    </div>
  );
};

export default BlockPalette;
