.error-node {
  box-shadow: 0 0 0 2px rgba(239, 68, 68, 0.7);
}

.error-node .react-flow__handle {
  background-color: #ef4444;
}

.error-message {
  color: #ef4444;
  font-size: 0.75rem;
  margin-top: 0.25rem;
}

.error-icon {
  color: #ef4444;
  margin-left: 0.25rem;
}

.disconnected-block {
  opacity: 0.7;
}

.error-tooltip {
  background-color: #fef2f2;
  border: 1px solid #fee2e2;
  border-radius: 0.375rem;
  padding: 0.5rem;
  max-width: 20rem;
  z-index: 50;
}

.error-tooltip-title {
  font-weight: 600;
  color: #b91c1c;
  margin-bottom: 0.25rem;
}

.error-tooltip-list {
  list-style-type: disc;
  padding-left: 1rem;
  color: #ef4444;
}

/* Improve dragging experience */
/* Reset all forced styles that might be causing issues */
.react-flow__edge,
.react-flow__handle,
.react-flow__edge-path {
  opacity: 1;
  visibility: visible;
  pointer-events: auto;
}

/* Fix node appearance */
.react-flow__node {
  background: transparent;
  border: none;
}

/* Improve node selection appearance */
.react-flow__node.selected {
  z-index: 10;
}

/* Improve edge appearance */
.react-flow__edge-path {
  stroke-width: 2px !important;
  stroke: #555 !important;
}

/* Improve handle appearance */
.react-flow__handle {
  width: 8px;
  height: 8px;
  border: 2px solid #fff;
}

/* Improve handle hover state */
.react-flow__handle:hover {
  background-color: #1a192b;
  transform: scale(1.2);
}

.react-flow__node-default.selected,
.react-flow__node-input.selected,
.react-flow__node-output.selected {
  box-shadow: 0 0 0 2px #1a192b;
}

/* Improve handle visibility */
.react-flow__handle {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  transition: transform 0.2s ease;
}

.react-flow__handle:hover {
  transform: scale(1.5);
}

/* Hide ReactFlow controls */
.react-flow__controls {
  display: none !important;
}
