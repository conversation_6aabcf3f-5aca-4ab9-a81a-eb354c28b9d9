import React, { useState } from 'react';
import { <PERSON><PERSON>, Position } from 'reactflow';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { DollarSign, Trash2, Star } from 'lucide-react';
import { AgentBlock } from '@/services/agentService';

interface PriceBlockProps {
  data: {
    id: string;
    dataPoint: string;
    timeframe?: string;
    lookback?: number;
    isEntryBlock: boolean;
    onUpdate: (properties: Partial<AgentBlock>) => void;
    onRemove: () => void;
    onSetAsEntry: () => void;
  };
  selected: boolean;
}

const PriceBlock: React.FC<PriceBlockProps> = ({ data, selected }) => {
  const [isEditing, setIsEditing] = useState(false);

  // Available price data points
  const dataPoints = [
    { value: 'open', label: 'Open Price' },
    { value: 'high', label: 'High Price' },
    { value: 'low', label: 'Low Price' },
    { value: 'close', label: 'Close Price' },
    { value: 'volume', label: 'Volume' },
    { value: 'current', label: 'Current Price' }
  ];

  // Available timeframes
  const timeframes = [
    { value: 'day', label: 'Daily' },
    { value: 'hour', label: 'Hourly' },
    { value: '15minute', label: '15 Minutes' }
  ];

  // Handle data point change
  const handleDataPointChange = (value: string) => {
    data.onUpdate({ dataPoint: value });
  };

  // Handle timeframe change
  const handleTimeframeChange = (value: string) => {
    data.onUpdate({ timeframe: value });
  };

  // Handle lookback change
  const handleLookbackChange = (value: string) => {
    const numValue = parseInt(value);
    if (!isNaN(numValue) && numValue >= 0) {
      data.onUpdate({ lookback: numValue });
    }
  };

  return (
    <div className="relative">
      {/* Refined input handle */}
      <Handle
        type="target"
        position={Position.Left}
        id="input"
        className="w-2.5 h-2.5 transition-all duration-400"
        style={{
          left: -5,
          background: 'linear-gradient(135deg, rgba(26, 26, 26, 0.9) 0%, rgba(20, 20, 20, 0.85) 100%)',
          border: '1px solid rgba(255, 255, 255, 0.06)',
          boxShadow: '0 2px 6px rgba(0,0,0,0.2), inset 0 1px 0 rgba(255,255,255,0.03)'
        }}
      />

      {/* Refined output handle */}
      <Handle
        type="source"
        position={Position.Right}
        id="output"
        className="w-2.5 h-2.5 transition-all duration-400"
        style={{
          right: -5,
          background: 'linear-gradient(135deg, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0.04) 100%)',
          border: '1px solid rgba(255, 255, 255, 0.12)',
          boxShadow: '0 2px 6px rgba(0,0,0,0.2), inset 0 1px 0 rgba(255,255,255,0.03)'
        }}
      />

      <div className={`group relative w-80 transition-all duration-400 ease-out`}>
        {/* Sophisticated material card with layered depth */}
        <div className={`
          relative overflow-hidden rounded-2xl backdrop-blur-sm transition-all duration-300 ease-out
          ${selected
            ? 'border border-white/20 bg-gradient-to-br from-[#1A1A1A]/95 to-[#141414]/90'
            : 'border border-white/[0.08] bg-gradient-to-br from-[#1A1A1A]/90 to-[#141414]/85'
          }
          ${data.isEntryBlock ? 'border-white/15' : ''}
        `}
        style={{
          boxShadow: selected
            ? '0 8px 32px rgba(0, 0, 0, 0.2), 0 0 0 1px rgba(255, 255, 255, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.04)'
            : '0 4px 20px rgba(0, 0, 0, 0.15), inset 0 1px 0 rgba(255, 255, 255, 0.03)'
        }}>

          {/* Layered material overlay */}
          <div className="absolute inset-0 bg-gradient-to-br from-white/[0.008] via-transparent to-black/[0.015]" />
          <div className="absolute inset-0 bg-gradient-to-t from-black/[0.01] to-transparent" />

          {/* Header with refined spacing */}
          <div className="relative px-5 py-4 border-b border-white/[0.04]">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                {/* Sophisticated icon container */}
                <div className="relative">
                  <div
                    className="w-10 h-10 rounded-xl border flex items-center justify-center"
                    style={{
                      background: 'linear-gradient(135deg, rgba(255, 255, 255, 0.06) 0%, rgba(255, 255, 255, 0.03) 100%)',
                      borderColor: 'rgba(255, 255, 255, 0.1)',
                      boxShadow: 'inset 0 1px 0 rgba(255, 255, 255, 0.03), 0 2px 4px rgba(0, 0, 0, 0.1)'
                    }}
                  >
                    <DollarSign className="h-5 w-5 text-white/70" />
                  </div>
                  {data.isEntryBlock && (
                    <div
                      className="absolute -top-1 -right-1 w-3 h-3 rounded-full border-2 border-[#1A1A1A]"
                      style={{
                        background: 'linear-gradient(135deg, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0.6) 100%)',
                        boxShadow: '0 2px 4px rgba(0, 0, 0, 0.2)'
                      }}
                    />
                  )}
                </div>

                {/* Refined typography */}
                <div>
                  <h3 className="text-sm font-medium text-white/90 tracking-wide">
                    Price Data
                  </h3>
                  <p className="text-xs text-white/50 mt-1 font-light tracking-wide">
                    Market price information
                  </p>
                </div>
              </div>

              {/* Refined action buttons */}
              <div className="flex items-center gap-1">
                {!data.isEntryBlock && (
                  <button
                    onClick={data.onSetAsEntry}
                    className="w-8 h-8 rounded-lg flex items-center justify-center transition-all duration-300"
                    style={{
                      background: 'rgba(255, 255, 255, 0.02)',
                      border: '1px solid rgba(255, 255, 255, 0.04)',
                      boxShadow: 'inset 0 1px 0 rgba(255, 255, 255, 0.01)'
                    }}
                    title="Set as entry block"
                  >
                    <Star className="h-3.5 w-3.5 text-white/45" />
                  </button>
                )}
                <button
                  onClick={() => setIsEditing(!isEditing)}
                  className="w-8 h-8 rounded-lg flex items-center justify-center transition-all duration-300"
                  style={{
                    background: 'rgba(255, 255, 255, 0.02)',
                    border: '1px solid rgba(255, 255, 255, 0.04)',
                    boxShadow: 'inset 0 1px 0 rgba(255, 255, 255, 0.01)'
                  }}
                  title={isEditing ? "Close editor" : "Edit block"}
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="1.5"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="h-3.5 w-3.5 text-white/45"
                  >
                    <path d="M17 3a2.85 2.83 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5Z" />
                    <path d="m15 5 4 4" />
                  </svg>
                </button>
                <button
                  onClick={data.onRemove}
                  className="w-8 h-8 rounded-lg flex items-center justify-center transition-all duration-300"
                  style={{
                    background: 'rgba(239, 68, 68, 0.06)',
                    border: '1px solid rgba(239, 68, 68, 0.12)',
                    boxShadow: 'inset 0 1px 0 rgba(255, 255, 255, 0.01)'
                  }}
                  title="Remove block"
                >
                  <Trash2 className="h-3.5 w-3.5 text-red-400/70" />
                </button>
              </div>
            </div>
          </div>
          {/* Premium content area */}
          <div className="relative px-5 py-4 space-y-4">
            <div>
              <label className="text-xs font-medium text-white/60 block mb-3 uppercase tracking-wider">Data Point</label>
              <Select
                value={data.dataPoint}
                onValueChange={handleDataPointChange}
              >
                <SelectTrigger className="h-10 text-sm bg-white/[0.02] border-white/[0.08] focus:border-white/20 transition-all duration-300 rounded-xl">
                  <SelectValue placeholder="Select data point" />
                </SelectTrigger>
                <SelectContent className="bg-[#1A1A1A]/95 border-white/[0.08] backdrop-blur-xl rounded-xl">
                  {dataPoints.map(point => (
                    <SelectItem key={point.value} value={point.value} className="text-sm text-white/85 focus:bg-white/10">
                      {point.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {isEditing && (
              <div className="space-y-4 pt-4 border-t border-white/[0.04]">
                <div>
                  <label className="text-xs font-medium text-white/60 block mb-3 uppercase tracking-wider">Timeframe</label>
                  <Select
                    value={data.timeframe || 'day'}
                    onValueChange={handleTimeframeChange}
                  >
                    <SelectTrigger className="h-10 text-sm bg-white/[0.02] border-white/[0.08] focus:border-white/20 transition-all duration-300 rounded-xl">
                      <SelectValue placeholder="Select timeframe" />
                    </SelectTrigger>
                    <SelectContent className="bg-[#1A1A1A]/95 border-white/[0.08] backdrop-blur-xl rounded-xl">
                      {timeframes.map(tf => (
                        <SelectItem key={tf.value} value={tf.value} className="text-sm text-white/85 focus:bg-white/10">
                          {tf.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <label className="text-xs font-medium text-white/60 block mb-3 uppercase tracking-wider">Lookback Periods</label>
                  <Input
                    type="number"
                    value={data.lookback || 0}
                    onChange={e => handleLookbackChange(e.target.value)}
                    className="h-10 text-sm bg-white/[0.02] border-white/[0.08] focus:border-white/20 transition-all duration-300 rounded-xl"
                    min={0}
                    placeholder="0 for current data"
                  />
                  <p className="text-xs text-white/35 mt-2 font-light tracking-wide">
                    {data.lookback === 0 || !data.lookback
                      ? "Using current data"
                      : `Looking back ${data.lookback} periods`}
                  </p>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default PriceBlock;
