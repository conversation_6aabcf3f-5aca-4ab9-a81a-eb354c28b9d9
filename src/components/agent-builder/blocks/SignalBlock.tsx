import React, { useState } from 'react';
import { <PERSON><PERSON>, Position } from 'reactflow';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Slider } from '@/components/ui/slider';
import { Target, Trash2, Star, TrendingUp, TrendingDown, Minus } from 'lucide-react';
import { AgentBlock } from '@/services/agentService';

interface SignalBlockProps {
  data: {
    id: string;
    signal: 'bullish' | 'bearish' | 'neutral';
    confidence: number;
    inputConnections: string[];
    isEntryBlock: boolean;
    onUpdate: (properties: Partial<AgentBlock>) => void;
    onRemove: () => void;
    onSetAsEntry: () => void;
  };
  selected: boolean;
}

const SignalBlock: React.FC<SignalBlockProps> = ({ data, selected }) => {
  const [isEditing, setIsEditing] = useState(false);

  // Handle signal change
  const handleSignalChange = (value: string) => {
    data.onUpdate({ signal: value as 'bullish' | 'bearish' | 'neutral' });
  };

  // Handle confidence change
  const handleConfidenceChange = (value: number[]) => {
    data.onUpdate({ confidence: value[0] });
  };

  // Get signal icon
  const getSignalIcon = () => {
    switch (data.signal) {
      case 'bullish':
        return <TrendingUp className="h-4 w-4 text-green-500" />;
      case 'bearish':
        return <TrendingDown className="h-4 w-4 text-red-500" />;
      default:
        return <Minus className="h-4 w-4 text-gray-500" />;
    }
  };

  // Get signal color
  const getSignalColor = () => {
    switch (data.signal) {
      case 'bullish':
        return 'text-green-500';
      case 'bearish':
        return 'text-red-500';
      default:
        return 'text-gray-500';
    }
  };

  return (
    <div className="relative">
      {/* Input handle */}
      <Handle
        type="target"
        position={Position.Left}
        id="input"
        style={{
          background: '#555',
          width: '10px',
          height: '10px',
          border: '2px solid white'
        }}
      />

      <Card className={`w-64 ${selected ? 'ring-2 ring-primary' : ''} ${data.isEntryBlock ? 'border-primary' : ''}`}>
        <CardHeader className="p-3 pb-0 flex flex-row items-center justify-between">
          <div className="flex items-center gap-2">
            <div className="w-6 h-6 rounded-full bg-primary/10 flex items-center justify-center">
              <Target className="h-3 w-3 text-primary" />
            </div>
            <CardTitle className="text-sm font-medium">Signal</CardTitle>
          </div>
          <div className="flex gap-1">
            {!data.isEntryBlock && (
              <Button
                variant="ghost"
                size="icon"
                className="h-6 w-6"
                onClick={data.onSetAsEntry}
                title="Set as entry block"
              >
                <Star className="h-3 w-3" />
              </Button>
            )}
            <Button
              variant="ghost"
              size="icon"
              className="h-6 w-6"
              onClick={() => setIsEditing(!isEditing)}
              title={isEditing ? "Close editor" : "Edit block"}
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="h-3 w-3"
              >
                <path d="M17 3a2.85 2.83 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5Z" />
                <path d="m15 5 4 4" />
              </svg>
            </Button>
            <Button
              variant="ghost"
              size="icon"
              className="h-6 w-6 text-destructive"
              onClick={data.onRemove}
              title="Remove block"
            >
              <Trash2 className="h-3 w-3" />
            </Button>
          </div>
        </CardHeader>
        <CardContent className="p-3 pt-2">
          <div className="space-y-3">
            <div>
              <label className="text-xs font-medium block mb-1">Signal</label>
              <Select
                value={data.signal}
                onValueChange={handleSignalChange}
              >
                <SelectTrigger className="h-8 text-xs">
                  <SelectValue placeholder="Select signal" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="bullish" className="text-xs">
                    <div className="flex items-center gap-2">
                      <TrendingUp className="h-3 w-3 text-green-500" />
                      <span>Bullish</span>
                    </div>
                  </SelectItem>
                  <SelectItem value="bearish" className="text-xs">
                    <div className="flex items-center gap-2">
                      <TrendingDown className="h-3 w-3 text-red-500" />
                      <span>Bearish</span>
                    </div>
                  </SelectItem>
                  <SelectItem value="neutral" className="text-xs">
                    <div className="flex items-center gap-2">
                      <Minus className="h-3 w-3 text-gray-500" />
                      <span>Neutral</span>
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <div className="flex justify-between items-center mb-1">
                <label className="text-xs font-medium">Confidence</label>
                <span className={`text-xs font-medium ${getSignalColor()}`}>
                  {data.confidence}%
                </span>
              </div>
              <Slider
                value={[data.confidence]}
                min={0}
                max={100}
                step={5}
                onValueChange={handleConfidenceChange}
                className="py-1"
              />
            </div>

            {isEditing && (
              <div className="mt-2 p-2 bg-muted/50 rounded-md">
                <p className="text-xs text-muted-foreground">
                  This block generates the final signal for the agent. Connect conditions or other blocks to the input to determine when this signal should be triggered.
                </p>
                <div className="flex items-center gap-2 mt-2">
                  {getSignalIcon()}
                  <span className={`text-xs font-medium ${getSignalColor()}`}>
                    {data.signal.charAt(0).toUpperCase() + data.signal.slice(1)} with {data.confidence}% confidence
                  </span>
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default SignalBlock;
