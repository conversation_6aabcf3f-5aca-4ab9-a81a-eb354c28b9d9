import React, { useState } from 'react';
import { <PERSON><PERSON>, Position } from 'reactflow';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Settings, Trash2, Star, TrendingUp } from 'lucide-react';
import { AgentBlock } from '@/services/agentService';

interface PercentageUpBlockProps {
  data: {
    id: string;
    percentage: number;
    inputConnections: string[];
    outputConnections: string[];
    isEntryBlock: boolean;
    onUpdate: (properties: Partial<AgentBlock>) => void;
    onRemove: () => void;
    onSetAsEntry: () => void;
  };
  selected: boolean;
}

const PercentageUpBlock: React.FC<PercentageUpBlockProps> = ({ data, selected }) => {
  const [isEditing, setIsEditing] = useState(false);

  // Handle percentage change
  const handlePercentageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = parseFloat(e.target.value) || 0;
    data.onUpdate({ percentage: Math.max(0, value) });
  };

  return (
    <div className="relative">
      {/* Input handle */}
      <Handle
        type="target"
        position={Position.Left}
        id="input"
        className="w-2.5 h-2.5 transition-all duration-400"
        style={{
          left: -5,
          background: 'linear-gradient(135deg, rgba(26, 26, 26, 0.9) 0%, rgba(20, 20, 20, 0.85) 100%)',
          border: '1px solid rgba(255, 255, 255, 0.06)',
          boxShadow: '0 2px 6px rgba(0,0,0,0.2), inset 0 1px 0 rgba(255,255,255,0.03)'
        }}
      />

      {/* Output handle - Bright Green for Action Block */}
      <Handle
        type="source"
        position={Position.Right}
        id="output"
        className="w-2.5 h-2.5 transition-all duration-400"
        style={{
          right: -5,
          background: 'linear-gradient(135deg, rgba(34, 197, 94, 0.9) 0%, rgba(22, 163, 74, 0.8) 100%)',
          border: '1px solid rgba(34, 197, 94, 0.6)',
          boxShadow: '0 2px 6px rgba(0,0,0,0.2), 0 0 12px rgba(34, 197, 94, 0.3), inset 0 1px 0 rgba(255,255,255,0.1)'
        }}
      />

      <div className="group relative w-80">
        {/* Bright Green Action Block Card */}
        <div className={`
          relative overflow-hidden rounded-2xl backdrop-blur-sm transition-all duration-300 ease-out
          ${selected
            ? 'border border-green-400/40 bg-gradient-to-br from-[#1A1A1A]/95 to-[#141414]/90'
            : 'border border-green-400/20 bg-gradient-to-br from-[#1A1A1A]/90 to-[#141414]/85'
          }
          ${data.isEntryBlock ? 'border-green-400/30' : ''}
        `}
        style={{
          boxShadow: selected
            ? '0 8px 32px rgba(0, 0, 0, 0.2), 0 0 0 1px rgba(34, 197, 94, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.04)'
            : '0 4px 20px rgba(0, 0, 0, 0.15), 0 0 0 1px rgba(34, 197, 94, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.03)'
        }}>

          {/* Layered material overlay */}
          <div className="absolute inset-0 bg-gradient-to-br from-green-500/[0.02] via-transparent to-black/[0.015]" />
          <div className="absolute inset-0 bg-gradient-to-t from-black/[0.01] to-transparent" />

          {/* Header with bright green accent */}
          <div className="relative px-5 py-4 border-b border-white/[0.04]">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                {/* Bright Green Icon Container */}
                <div className="relative">
                  <div
                    className="w-10 h-10 rounded-xl border flex items-center justify-center"
                    style={{
                      background: 'linear-gradient(135deg, rgba(34, 197, 94, 0.15) 0%, rgba(22, 163, 74, 0.1) 100%)',
                      borderColor: 'rgba(34, 197, 94, 0.3)',
                      boxShadow: 'inset 0 1px 0 rgba(255, 255, 255, 0.05), 0 2px 4px rgba(0, 0, 0, 0.1), 0 0 8px rgba(34, 197, 94, 0.1)'
                    }}
                  >
                    <TrendingUp className="h-5 w-5 text-green-400" />
                  </div>
                  {data.isEntryBlock && (
                    <div
                      className="absolute -top-1 -right-1 w-3 h-3 rounded-full border-2 border-[#1A1A1A]"
                      style={{
                        background: 'linear-gradient(135deg, rgba(34, 197, 94, 0.9) 0%, rgba(22, 163, 74, 0.8) 100%)',
                        boxShadow: '0 2px 4px rgba(0, 0, 0, 0.2)'
                      }}
                    />
                  )}
                </div>

                {/* Title with green accent */}
                <div>
                  <h3 className="text-sm font-medium text-white/90 tracking-wide">
                    +{data.percentage}% Up
                  </h3>
                  <p className="text-xs text-green-400/80 mt-1 font-light tracking-wide">
                    Percentage gain trigger
                  </p>
                </div>
              </div>

              {/* Action buttons */}
              <div className="flex items-center gap-1">
                {!data.isEntryBlock && (
                  <button
                    onClick={data.onSetAsEntry}
                    className="w-8 h-8 rounded-lg flex items-center justify-center transition-all duration-300"
                    style={{
                      background: 'rgba(255, 255, 255, 0.02)',
                      border: '1px solid rgba(255, 255, 255, 0.04)',
                      boxShadow: 'inset 0 1px 0 rgba(255, 255, 255, 0.01)'
                    }}
                    title="Set as entry block"
                  >
                    <Star className="h-3.5 w-3.5 text-white/45" />
                  </button>
                )}
                <button
                  onClick={() => setIsEditing(!isEditing)}
                  className="w-8 h-8 rounded-lg flex items-center justify-center transition-all duration-300"
                  style={{
                    background: 'rgba(255, 255, 255, 0.02)',
                    border: '1px solid rgba(255, 255, 255, 0.04)',
                    boxShadow: 'inset 0 1px 0 rgba(255, 255, 255, 0.01)'
                  }}
                  title="Edit block"
                >
                  <Settings className="h-3.5 w-3.5 text-white/45" />
                </button>
                <button
                  onClick={data.onRemove}
                  className="w-8 h-8 rounded-lg flex items-center justify-center transition-all duration-300"
                  style={{
                    background: 'rgba(239, 68, 68, 0.06)',
                    border: '1px solid rgba(239, 68, 68, 0.12)',
                    boxShadow: 'inset 0 1px 0 rgba(255, 255, 255, 0.01)'
                  }}
                  title="Remove block"
                >
                  <Trash2 className="h-3.5 w-3.5 text-red-400/70" />
                </button>
              </div>
            </div>
          </div>

          {/* Content area */}
          <div className="relative px-5 py-4 space-y-4">
            <div className="text-xs text-white/60 font-light tracking-wide">
              Triggers when price moves up by {data.percentage}%
            </div>

            {isEditing && (
              <div className="space-y-4 pt-4 border-t border-white/[0.04]">
                <div>
                  <Label htmlFor="percentage" className="text-xs font-medium text-white/60 block mb-3 uppercase tracking-wider">Percentage Threshold</Label>
                  <Input
                    id="percentage"
                    type="number"
                    min="0"
                    step="0.1"
                    value={data.percentage}
                    onChange={handlePercentageChange}
                    className="h-10 text-sm bg-white/[0.02] border-white/[0.08] focus:border-green-400/30 transition-all duration-300 rounded-xl"
                    placeholder="e.g., 5.0"
                  />
                  <p className="text-xs text-white/35 mt-2 font-light tracking-wide">
                    Enter the percentage gain that will trigger this block (e.g., 5 for +5%)
                  </p>
                </div>

                <div className="mt-4 p-3 bg-green-500/[0.03] border border-green-400/10 rounded-xl">
                  <p className="text-xs text-white/60 font-light leading-relaxed">
                    This is an action block that triggers when the stock price increases by the specified percentage.
                    Unlike trigger blocks, this allows the agent to continue executing additional blocks after the percentage move occurs.
                  </p>
                  <div className="flex items-center gap-2 mt-3">
                    <TrendingUp className="h-3 w-3 text-green-400" />
                    <span className="text-xs font-medium text-green-400">
                      Triggers on +{data.percentage}% price movement
                    </span>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default PercentageUpBlock;
