import React, { useState, useEffect } from 'react';

interface TypingPlaceholderProps {
  examples: string[];
  className?: string;
}

const TypingPlaceholder: React.FC<TypingPlaceholderProps> = ({ examples, className = "" }) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [displayText, setDisplayText] = useState('');
  const [isTyping, setIsTyping] = useState(true);
  const [isDeleting, setIsDeleting] = useState(false);

  useEffect(() => {
    const currentExample = examples[currentIndex];
    
    const typeSpeed = 100; // Typing speed in ms
    const deleteSpeed = 50; // Deleting speed in ms
    const pauseTime = 2000; // Pause time at end of word in ms
    const fadeTime = 300; // Fade transition time in ms

    let timeout: NodeJS.Timeout;

    if (isTyping && !isDeleting) {
      // Typing forward
      if (displayText.length < currentExample.length) {
        timeout = setTimeout(() => {
          setDisplayText(currentExample.slice(0, displayText.length + 1));
        }, typeSpeed);
      } else {
        // Finished typing, pause then start deleting
        timeout = setTimeout(() => {
          setIsDeleting(true);
          setIsTyping(false);
        }, pauseTime);
      }
    } else if (isDeleting && !isTyping) {
      // Deleting backward
      if (displayText.length > 0) {
        timeout = setTimeout(() => {
          setDisplayText(displayText.slice(0, -1));
        }, deleteSpeed);
      } else {
        // Finished deleting, move to next example
        setIsDeleting(false);
        setCurrentIndex((prev) => (prev + 1) % examples.length);
        
        // Small pause before starting to type next word
        timeout = setTimeout(() => {
          setIsTyping(true);
        }, fadeTime);
      }
    }

    return () => clearTimeout(timeout);
  }, [displayText, isTyping, isDeleting, currentIndex, examples]);

  return (
    <span className={`${className} transition-opacity duration-300`}>
      {displayText}
      <span className="animate-pulse">|</span>
    </span>
  );
};

export default TypingPlaceholder;
