import * as React from "react";
import { useState, useEffect, useRef } from "react";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { useToast } from "@/components/ui/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { useNavigate, useParams } from "react-router-dom";
import { WelcomeHeading } from "@/components/ui/WelcomeHeading";
import MessageCounter from './MessageCounter';
import MinimalLoadingMessage from './MinimalLoadingMessage';
import { useUserLimits } from '@/hooks/useUserLimits';
import { useAuth } from '@/contexts/AuthContext';

// =============================================
// TYPE DEFINITIONS
// =============================================
interface Message {
  role: string;
  content: {
    text: string | null;
    marketData?: any;
    webResults?: any[];
    symbol?: string | null;
    searchQueries?: string[];
    evaAnalysis?: any;
    symbols?: string[];
    isLoading?: boolean;
    analysisProgress?: any;
    aiAnalysis?: string;
    symbolTypes?: any;
    loadingPlaceholder?: boolean;
    isGeneralMessage?: boolean;
  };
}

interface ChatAIResponse {
  symbols: string[];
  symbolTypes: { [key: string]: 'STOCK' | 'CRYPTO' | 'MARKET' };
  marketData: any;
  isGeneralMessage: boolean;
}

// =============================================
// MINIMAL CHAT INTERFACE COMPONENT
// =============================================
const MinimalChatInterface: React.FC = () => {
  // =============================================
  // STATE VARIABLES
  // =============================================
  const [message, setMessage] = useState("");
  const [messages, setMessages] = useState<Message[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  // Get authentication state from useAuth hook
  const { isAuthenticated } = useAuth();

  // Add useUserLimits hook
  const {
    messagesRemaining,
    messagesLimit,
    incrementMessagesUsed,
    isLoading: isLoadingLimits,
    planType,
    hasReachedLimit
  } = useUserLimits();

  // =============================================
  // REFS, HOOKS, AND NAVIGATION
  // =============================================
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const { toast } = useToast();
  const navigate = useNavigate();
  const { id: currentChatId } = useParams();

  // Reset state for new chats
  useEffect(() => {
    if (!currentChatId) {
      setMessages([]);
      setIsLoading(false);
    }
  }, [currentChatId]);

  // Load existing chat messages
  useEffect(() => {
    if (!currentChatId || !isAuthenticated) {
      setMessages([]);
      return;
    }

    const loadChat = async () => {
      try {
        const { data: chatMessages, error } = await supabase
          .from('messages')
          .select('*')
          .eq('chat_id', currentChatId)
          .order('created_at', { ascending: true });

        if (error) throw error;

        if (chatMessages) {
          const formattedMessages = chatMessages.map(msg => ({
            role: msg.role,
            content: typeof msg.content === 'string' ? JSON.parse(msg.content) : msg.content
          }));
          setMessages(formattedMessages);
        }
      } catch (error) {
        toast({
          title: "Error",
          description: "Failed to load chat messages. Please try refreshing the page.",
          variant: "destructive"
        });
      }
    };

    loadChat();
  }, [currentChatId, isAuthenticated]);

  // =============================================
  // MESSAGE SUBMISSION HANDLER
  // =============================================
  const queryClient = useQueryClient();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Prevent free users from sending messages
    if (planType === 'free') {
      navigate('/subscription/manage');
      return;
    }

    if (!message.trim() || isLoading) return;

    // Check message limits
    const limitReached = await hasReachedLimit();
    if (limitReached) {
      toast({
        title: "Message limit reached",
        description: `You've reached your ${messagesLimit} message limit for your ${planType} plan. Please upgrade for more messages.`,
        variant: "destructive"
      });
      return;
    }

    try {
      setIsLoading(true);

      // Add user message immediately
      const userMessage: Message = {
        role: 'user',
        content: { text: message }
      };

      // Add loading message
      const loadingMessage: Message = {
        role: 'assistant',
        content: {
          text: null,
          loadingPlaceholder: true,
          isLoading: true
        }
      };

      setMessages(prev => [...prev, userMessage, loadingMessage]);
      setMessage("");

      // Auto-resize textarea
      if (textareaRef.current) {
        textareaRef.current.style.height = 'auto';
      }

      // Make API call
      const { data: response, error: marketError } = await supabase.functions.invoke<ChatAIResponse>('chat-ai', {
        body: {
          messages: [{ role: 'user', content: message }]
        }
      });

      if (marketError) {
        throw new Error(`API Error: ${marketError.message}`);
      }

      if (!response) {
        throw new Error('No response received from chat-ai function');
      }

      // Update the loading message with the response
      setMessages(prev => {
        const updated = [...prev];
        const lastIndex = updated.length - 1;
        if (updated[lastIndex] && updated[lastIndex].content.loadingPlaceholder) {
          updated[lastIndex] = {
            role: 'assistant',
            content: {
              text: response.isGeneralMessage ? response.marketData : null,
              marketData: response.marketData,
              symbols: response.symbols,
              symbolTypes: response.symbolTypes,
              isGeneralMessage: response.isGeneralMessage,
              isLoading: false,
              loadingPlaceholder: false
            }
          };
        }
        return updated;
      });

      // Increment message count
      await incrementMessagesUsed();

    } catch (error) {
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to send message. Please try again.',
        variant: 'destructive'
      });

      // Remove the loading message on error
      setMessages(prev => prev.slice(0, -1));
    } finally {
      setIsLoading(false);
    }
  };

  // =============================================
  // RENDER MINIMAL INTERFACE
  // =============================================
  return (
    <div className="h-full bg-[#141414] text-white flex flex-col font-hanken-grotesk">
      {/* Message Counter */}
      <MessageCounter
        messagesRemaining={messagesRemaining}
        messagesLimit={messagesLimit}
        planType={planType}
        isLoading={isLoadingLimits}
      />

      {/* Main Content - Document-like Writing Experience */}
      <div className="flex-1 px-8 py-8 overflow-y-auto">
        <div className="max-w-4xl mx-auto">
          {/* Header - Always visible */}
          <div className="mb-8">
            <WelcomeHeading />
          </div>

          {/* Writing Area - No boxes, just text flow */}
          <div className="space-y-6">
            {/* Show "Start typing" when no messages */}
            {messages.length === 0 && (
              <div className="text-left">
                <p className="text-white/60 text-lg">Start typing...</p>
              </div>
            )}

            {/* Messages flow naturally like a document */}
            {messages.map((msg, index) => (
              <div key={index} className="space-y-4">
                {msg.role === 'user' ? (
                  <div className="text-left">
                    <p className="text-white text-lg leading-relaxed">{msg.content.text}</p>
                  </div>
                ) : msg.content.loadingPlaceholder ? (
                  <div className="text-left">
                    <div className="flex items-center gap-2 text-white/60">
                      <div className="w-1 h-1 bg-white/60 rounded-full animate-pulse"></div>
                      <div className="w-1 h-1 bg-white/60 rounded-full animate-pulse" style={{ animationDelay: '0.2s' }}></div>
                      <div className="w-1 h-1 bg-white/60 rounded-full animate-pulse" style={{ animationDelay: '0.4s' }}></div>
                      <span className="ml-2">Analyzing...</span>
                    </div>
                  </div>
                ) : (
                  <div className="text-left">
                    <MinimalLoadingMessage
                      userQuery={messages[index - 1]?.content?.text || ""}
                      isComplete={!msg.content.isLoading}
                      symbols={msg.content.symbols}
                      finalContent={msg.content.aiAnalysis || msg.content.text}
                      marketData={msg.content.marketData}
                      isLoading={msg.content.isLoading}
                      symbolTypes={msg.content.symbolTypes}
                      isGeneralMessage={msg.content.isGeneralMessage}
                      onRegenerate={async () => {
                        // Regeneration logic here
                      }}
                    />
                  </div>
                )}
              </div>
            ))}

            {/* Input Area - Inline with the text flow */}
            <div className="text-left">
              <form onSubmit={handleSubmit}>
                <textarea
                  ref={textareaRef}
                  className="w-full bg-transparent border-none text-white text-lg placeholder:text-white/40 resize-none focus:outline-none leading-relaxed"
                  value={message}
                  onChange={(e) => {
                    setMessage(e.target.value);
                    // Auto-resize
                    e.target.style.height = 'auto';
                    e.target.style.height = `${e.target.scrollHeight}px`;
                  }}
                  placeholder={messages.length === 0 ? "What can we analyze for you today?" : "Continue the conversation..."}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter' && !e.shiftKey) {
                      e.preventDefault();
                      handleSubmit(e as any);
                    }
                  }}
                  rows={1}
                  style={{ minHeight: '28px' }}
                />

                {/* Loading indicator appears inline */}
                {isLoading && (
                  <div className="flex items-center gap-2 text-white/60 mt-2">
                    <div className="w-1 h-1 bg-white/60 rounded-full animate-pulse"></div>
                    <div className="w-1 h-1 bg-white/60 rounded-full animate-pulse" style={{ animationDelay: '0.2s' }}></div>
                    <div className="w-1 h-1 bg-white/60 rounded-full animate-pulse" style={{ animationDelay: '0.4s' }}></div>
                    <span className="ml-2">Processing...</span>
                  </div>
                )}
              </form>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MinimalChatInterface;
