import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { cn } from '@/lib/utils';
import {
  House,
  TrendingUp,
  <PERSON><PERSON>hart,
  Hammer,
  MessagesSquare,
  Zap,
  Activity,
  Settings,
  User,
  Crown,
  Info,
  FileText,
  Shield,
  ChevronLeft,
  ChevronRight
} from 'lucide-react';
import AuthButton from '@/components/auth/AuthButton';

interface SidebarItem {
  id: string;
  label: string;
  icon: React.ReactNode;
  path: string;
  description?: string;
}

interface AppSidebarProps {
  onMinimizedChange?: (isMinimized: boolean) => void;
}

const AppSidebar: React.FC<AppSidebarProps> = ({ onMinimizedChange }) => {
  const navigate = useNavigate();
  const location = useLocation();

  // Sidebar minimizer state with persistence
  const [isMinimized, setIsMinimized] = useState(() => {
    const saved = localStorage.getItem('sidebar-minimized');
    return saved ? JSON.parse(saved) : false;
  });

  // Persist minimizer state and notify parent
  useEffect(() => {
    localStorage.setItem('sidebar-minimized', JSON.stringify(isMinimized));
    onMinimizedChange?.(isMinimized);
  }, [isMinimized, onMinimizedChange]);

  const toggleMinimized = () => {
    setIsMinimized(!isMinimized);
  };

  // Main navigation items
  const mainNavItems: SidebarItem[] = [
    {
      id: 'home',
      label: 'Home',
      icon: <House className="w-4 h-4" />,
      path: '/home'
    },
    {
      id: 'stock-scanner',
      label: 'Stock Scanner',
      icon: <TrendingUp className="w-4 h-4" />,
      path: '/agent-scanner'
    },
    {
      id: 'portfolio-builder',
      label: 'Portfolio Builder',
      icon: <PieChart className="w-4 h-4" />,
      path: '/portfolio-builder'
    },
    {
      id: 'agent-builder',
      label: 'Agent Builder',
      icon: <Hammer className="w-4 h-4" />,
      path: '/agent-builder'
    },
    {
      id: 'chat',
      label: 'Chat Interface',
      icon: <MessagesSquare className="w-4 h-4" />,
      path: '/chat'
    }
  ];

  // Secondary navigation items
  const secondaryNavItems: SidebarItem[] = [
    {
      id: 'agents',
      label: 'Trading Agents',
      icon: <Zap className="w-4 h-4" />,
      path: '/agents'
    },
    {
      id: 'agent-backtesting',
      label: 'Backtesting',
      icon: <Activity className="w-4 h-4" />,
      path: '/agent-backtesting'
    }
  ];

  // Settings navigation items
  const settingsNavItems: SidebarItem[] = [
    {
      id: 'profile',
      label: 'Profile',
      icon: <User className="w-4 h-4" />,
      path: '/settings'
    },
    {
      id: 'subscription',
      label: 'Subscription',
      icon: <Crown className="w-4 h-4" />,
      path: '/subscription/manage'
    },
    {
      id: 'about',
      label: 'About',
      icon: <Info className="w-4 h-4" />,
      path: '/about'
    },
    {
      id: 'terms',
      label: 'Terms of Service',
      icon: <FileText className="w-4 h-4" />,
      path: '/terms'
    },
    {
      id: 'privacy',
      label: 'Privacy Policy',
      icon: <Shield className="w-4 h-4" />,
      path: '/privacy'
    }
  ];

  const isActiveRoute = (path: string) => {
    if (path === '/home') {
      return location.pathname === '/' || location.pathname === '/home';
    }
    if (path === '/chat') {
      return location.pathname === '/chat' || location.pathname.startsWith('/chat/');
    }
    return location.pathname === path || location.pathname.startsWith(path + '/');
  };

  const handleNavigation = (path: string) => {
    navigate(path);
  };

  return (
    <div
      className={cn(
        "h-full flex flex-col relative transition-all duration-300 ease-in-out",
        isMinimized ? "w-16" : "w-52"
      )}
    >
      {/* Solid Background */}
      <div
        className="absolute inset-0 z-0"
        style={{
          backgroundColor: '#141414'
        }}
      />


      {/* Logo/Brand Section */}
      <div className={cn("relative z-10", isMinimized ? "px-3 py-4" : "px-4 py-5")}>
        <div className={cn("flex items-center", isMinimized ? "justify-center" : "gap-2.5")}>
          <img
            src="http://thecodingkid.oyosite.com/logo_only.png"
            alt="Osis Logo"
            className="h-6 w-6 drop-shadow-sm flex-shrink-0"
          />
          {!isMinimized && (
            <span className="text-lg font-medium text-white tracking-tight font-hanken-grotesk">Osis</span>
          )}
        </div>
      </div>

      {/* Main Navigation */}
      <div className="flex-1 overflow-y-auto relative z-10">
        <div className="p-4">
          <div className="space-y-2">
            {mainNavItems.map((item) => (
              <button
                key={item.id}
                onClick={() => handleNavigation(item.path)}
                className={cn(
                  "w-full flex items-center rounded-lg text-left transition-all duration-300 group font-mono",
                  isMinimized ? "justify-center px-2 py-2.5" : "gap-2.5 px-3 py-2",
                  isActiveRoute(item.path)
                    ? "bg-white/[0.08] text-white shadow-[inset_0_2px_6px_rgba(0,0,0,0.15),inset_0_1px_0_rgba(255,255,255,0.1)] border border-white/[0.05]"
                    : "text-white/70 hover:text-white hover:bg-white/[0.04] hover:shadow-[inset_0_1px_3px_rgba(0,0,0,0.1)]"
                )}
                title={isMinimized ? item.label : undefined}
              >
                <div className={cn(
                  "flex-shrink-0 transition-colors duration-300",
                  isActiveRoute(item.path) ? "text-white" : "text-white/80 group-hover:text-white"
                )}>
                  {item.icon}
                </div>
                {!isMinimized && (
                  <div className="flex-1 min-w-0">
                    <div className="font-medium text-xs truncate tracking-wider uppercase">
                      {item.label}
                    </div>
                  </div>
                )}
              </button>
            ))}
          </div>

          {/* Secondary Navigation */}
          <div className="mt-6">
            {!isMinimized && (
              <div className="px-2.5 mb-3">
                <span className="uppercase text-xs font-medium text-white/40 tracking-wider font-mono">TOOLS</span>
              </div>
            )}
            <div className="space-y-2">
              {secondaryNavItems.map((item) => (
                <button
                  key={item.id}
                  onClick={() => handleNavigation(item.path)}
                  className={cn(
                    "w-full flex items-center rounded-lg text-left transition-all duration-300 group font-mono",
                    isMinimized ? "justify-center px-2 py-2.5" : "gap-2.5 px-3 py-2",
                    isActiveRoute(item.path)
                      ? "bg-white/[0.08] text-white shadow-[inset_0_2px_6px_rgba(0,0,0,0.15),inset_0_1px_0_rgba(255,255,255,0.1)] border border-white/[0.05]"
                      : "text-white/70 hover:text-white hover:bg-white/[0.04] hover:shadow-[inset_0_1px_3px_rgba(0,0,0,0.1)]"
                  )}
                  title={isMinimized ? item.label : undefined}
                >
                  <div className={cn(
                    "flex-shrink-0 transition-colors duration-300",
                    isActiveRoute(item.path) ? "text-white" : "text-white/80 group-hover:text-white"
                  )}>
                    {item.icon}
                  </div>
                  {!isMinimized && (
                    <div className="flex-1 min-w-0">
                      <div className="font-medium text-xs truncate tracking-wider uppercase">
                        {item.label}
                      </div>
                    </div>
                  )}
                </button>
              ))}
            </div>
          </div>

          {/* Settings Navigation */}
          <div className="mt-6">
            {!isMinimized && (
              <div className="px-2.5 mb-3">
                <span className="uppercase text-xs font-medium text-white/40 tracking-wider font-mono">SETTINGS</span>
              </div>
            )}
            <div className="space-y-2">
              {settingsNavItems.map((item) => (
                <button
                  key={item.id}
                  onClick={() => handleNavigation(item.path)}
                  className={cn(
                    "w-full flex items-center rounded-lg text-left transition-all duration-300 group font-mono",
                    isMinimized ? "justify-center px-2 py-2.5" : "gap-2.5 px-3 py-2",
                    isActiveRoute(item.path)
                      ? "bg-white/[0.08] text-white shadow-[inset_0_2px_6px_rgba(0,0,0,0.15),inset_0_1px_0_rgba(255,255,255,0.1)] border border-white/[0.05]"
                      : "text-white/70 hover:text-white hover:bg-white/[0.04] hover:shadow-[inset_0_1px_3px_rgba(0,0,0,0.1)]"
                  )}
                  title={isMinimized ? item.label : undefined}
                >
                  <div className={cn(
                    "flex-shrink-0 transition-colors duration-300",
                    isActiveRoute(item.path) ? "text-white" : "text-white/80 group-hover:text-white"
                  )}>
                    {item.icon}
                  </div>
                  {!isMinimized && (
                    <div className="flex-1 min-w-0">
                      <div className="font-medium text-xs truncate tracking-wider uppercase">
                        {item.label}
                      </div>
                    </div>
                  )}
                </button>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Profile Section */}
      {!isMinimized && (
        <div className="p-3 relative z-10">
          <div className="text-[10px] text-white/40 font-mono tracking-wider uppercase">
            OSIS.AI
          </div>
        </div>
      )}

      {/* Minimizer Toggle Button - Bottom Positioned */}
      <div className="relative z-10 p-3">
        <button
          onClick={toggleMinimized}
          className={cn(
            "w-full flex items-center justify-center rounded-lg transition-all duration-300 group",
            "bg-white/[0.06] hover:bg-white/[0.08] border border-white/[0.05]",
            "shadow-[inset_0_2px_6px_rgba(0,0,0,0.15),inset_0_1px_0_rgba(255,255,255,0.1)] hover:shadow-[inset_0_2px_8px_rgba(0,0,0,0.2)]",
            isMinimized ? "py-3" : "py-2.5 gap-2"
          )}
        >
          <div className="flex-shrink-0 text-white/70 group-hover:text-white transition-colors duration-300">
            {isMinimized ? (
              <ChevronRight className="w-4 h-4" />
            ) : (
              <ChevronLeft className="w-4 h-4" />
            )}
          </div>
          {!isMinimized && (
            <span className="text-xs font-medium text-white/70 group-hover:text-white transition-colors duration-300 font-mono uppercase tracking-wider">
              COLLAPSE
            </span>
          )}
        </button>
      </div>
    </div>
  );
};

export default AppSidebar;
