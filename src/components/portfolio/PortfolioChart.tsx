import React from 'react';
import ReactECharts from 'echarts-for-react';
import { Card } from "@/components/ui/card";

interface ChartDataPoint {
  date: string;
  value: number;
}

interface PortfolioChartProps {
  data: ChartDataPoint[];
  title?: string;
  height?: number;
  loading?: boolean;
  projectionData?: ChartDataPoint[];
  timeframe?: string;
}

const PortfolioChart: React.FC<PortfolioChartProps> = ({
  data,
  title = 'Portfolio Performance',
  height = 300,
  loading = false,
  projectionData = [],
  timeframe = '1Y' // eslint-disable-line @typescript-eslint/no-unused-vars
}) => {
  if (loading) {
    return (
      <Card className="w-full h-[300px] flex items-center justify-center bg-muted/20">
        <div className="animate-pulse flex flex-col items-center">
          <div className="h-8 w-8 rounded-full border-2 border-t-transparent border-white/30 animate-spin"></div>
          <p className="mt-2 text-sm text-muted-foreground">Loading chart data...</p>
        </div>
      </Card>
    );
  }

  if (!data || data.length === 0) {
    return (
      <Card className="w-full h-[300px] flex items-center justify-center bg-muted/20">
        <p className="text-muted-foreground">No data available</p>
      </Card>
    );
  }

  // Combine historical and projection data
  const allData = [...data, ...projectionData];
  const dates = allData.map(item => item.date);
  const values = allData.map(item => item.value);

  // Historical data only for percentage calculation
  const historicalValues = data.map(item => item.value);
  const startValue = historicalValues[0];
  const currentValue = historicalValues[historicalValues.length - 1];
  const percentChange = ((currentValue - startValue) / startValue) * 100;

  // Calculate min and max values for y-axis scaling (including projections)
  const minValue = Math.min(...values) * 0.9;
  const maxValue = Math.max(...values) * 1.1;



  const option = {
    title: [
      {
        text: title || 'Agent Returns',
        left: '24px',
        top: '24px',
        textStyle: {
          color: 'rgba(255, 255, 255, 0.9)',
          fontWeight: 600,
          fontSize: 18,
          fontFamily: 'Hanken Grotesk'
        }
      },
      {
        text: `${percentChange >= 0 ? '+' : ''}${percentChange.toFixed(2)}%`,
        left: '24px',
        top: '52px',
        textStyle: {
          color: percentChange >= 0 ? 'rgba(78, 184, 151, 0.95)' : 'rgba(229, 128, 128, 0.95)',
          fontWeight: 500,
          fontSize: 28,
          fontFamily: 'Hanken Grotesk'
        }
      }
    ],
    tooltip: {
      trigger: 'axis' as const,
      formatter: function(params: any) {
        const dataIndex = params[0].dataIndex;
        const date = dates[dataIndex];
        const value = values[dataIndex].toFixed(2);
        const initialValue = values[0];
        const percentChange = (((values[dataIndex] - initialValue) / initialValue) * 100);
        const percentChangeStr = percentChange.toFixed(2);

        // Format date to be more readable
        const formattedDate = new Date(date).toLocaleDateString('en-US', {
          year: 'numeric',
          month: 'short'
        });

        return `
          <div style="font-size:14px;color:#fff;font-weight:500;margin-bottom:7px">${formattedDate}</div>
          <div style="font-size:13px;color:#fff;font-weight:400">
            <span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:#5470c6"></span>
            Value: $${value}
          </div>
          <div style="font-size:13px;color:#fff;font-weight:400">
            <span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:${percentChange >= 0 ? '#91cc75' : '#ee6666'}"></span>
            Change: ${percentChangeStr}%
          </div>
        `;
      },
      backgroundColor: 'rgba(20, 20, 20, 0.9)',
      borderColor: 'rgba(255, 255, 255, 0.1)',
      textStyle: {
        color: '#fff'
      }
    },
    grid: {
      left: '0%',
      right: '0%',
      bottom: '0%',
      top: 100,
      containLabel: false
    },
    xAxis: {
      type: 'category' as const,
      data: dates,
      show: false,
      boundaryGap: false,
      axisLine: { show: false },
      axisTick: { show: false },
      splitLine: { show: false }
    },
    yAxis: {
      type: 'value' as const,
      min: minValue,
      max: maxValue,
      show: false,
      axisLine: { show: false },
      axisTick: { show: false },
      splitLine: { show: false }
    },
    series: [
      // Historical data with area fill - extends across full width
      {
        data: values, // Use all values (historical + projection)
        type: 'line' as const,
        smooth: true,
        symbol: 'none',
        sampling: 'none' as const,
        lineStyle: {
          width: 3,
          color: percentChange >= 0 ? 'rgba(78, 184, 151, 0.9)' : 'rgba(229, 128, 128, 0.9)'
        },
        areaStyle: {
          color: {
            type: 'linear' as const,
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [{
              offset: 0,
              color: percentChange >= 0 ? 'rgba(78, 184, 151, 0.4)' : 'rgba(229, 128, 128, 0.4)'
            }, {
              offset: 0.7,
              color: percentChange >= 0 ? 'rgba(78, 184, 151, 0.1)' : 'rgba(229, 128, 128, 0.1)'
            }, {
              offset: 1,
              color: 'rgba(0, 0, 0, 0)'
            }]
          }
        },
        connectNulls: false,
        step: false as const,
        // Mark the projection area with subtle styling
        markArea: projectionData.length > 0 ? {
          silent: true,
          itemStyle: {
            color: 'rgba(255, 255, 255, 0.02)'
          },
          data: [[
            { xAxis: data.length - 1 },
            { xAxis: dates.length - 1 }
          ]]
        } : undefined
      },
      // Projection line overlay (dashed, no fill) - only for visual distinction
      ...(projectionData.length > 0 ? [{
        data: Array(data.length).fill(null).concat(projectionData.map(item => item.value)),
        type: 'line' as const,
        smooth: true,
        symbol: 'none',
        lineStyle: {
          width: 2,
          color: 'rgba(255, 255, 255, 0.6)',
          type: 'dashed' as const
        },
        connectNulls: false
      }] : [])
    ]
  };

  return (
    <div className="w-full h-full">
      <ReactECharts
        option={option as any}
        style={{ height: `${height}px`, width: '100%' }}
        className="bg-[#0D0D0D] rounded-md w-full"
        opts={{ renderer: 'canvas', width: 'auto', height: 'auto' }}
      />
    </div>
  );
};

export default PortfolioChart;
