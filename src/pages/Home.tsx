import React from 'react';
import { useNavigate } from 'react-router-dom';
import {
  MessageCircle,
  TrendingUp,
  Briefcase,
  Hammer,
  BarChart3,
  ArrowRight,
  Bot,
  Activity,
  ChevronRight
} from 'lucide-react';

const Home: React.FC = () => {
  const navigate = useNavigate();

  const mainFeatures = [
    {
      title: 'Chat Interface',
      description: 'AI-powered market analysis and trading insights',
      icon: <MessageCircle className="w-5 h-5" />,
      path: '/chat',
      accent: 'emerald'
    },
    {
      title: 'Stock Scanner',
      description: 'Real-time market scanning and opportunity detection',
      icon: <TrendingUp className="w-5 h-5" />,
      path: '/agent-scanner',
      accent: 'emerald'
    },
    {
      title: 'Portfolio Builder',
      description: 'Intelligent portfolio construction and management',
      icon: <Briefcase className="w-5 h-5" />,
      path: '/portfolio-builder',
      accent: 'emerald'
    },
    {
      title: 'Agent Builder',
      description: 'Visual trading agent creation and deployment',
      icon: <Hammer className="w-5 h-5" />,
      path: '/agent-builder',
      accent: 'emerald'
    }
  ];

  const tools = [
    {
      title: 'Trading Agents',
      description: 'Manage and monitor your AI trading agents',
      icon: <Bot className="w-4 h-4" />,
      path: '/agents'
    },
    {
      title: 'Backtesting',
      description: 'Test and validate your trading strategies',
      icon: <Activity className="w-4 h-4" />,
      path: '/agent-backtesting'
    }
  ];

  return (
    <div className="h-full bg-[#141414] text-white font-hanken-grotesk">
      <div className="max-w-6xl mx-auto px-8 py-12">

        {/* Header */}
        <div className="mb-16">
          <h1 className="text-3xl font-bold text-white mb-3 font-hanken-grotesk">
            OSIS Trading Platform
          </h1>
          <p className="text-white/60 text-lg leading-relaxed max-w-2xl">
            AI-powered trading tools for market analysis, portfolio management, and automated trading strategies.
          </p>
        </div>

        {/* Main Features */}
        <div className="mb-16">
          <h2 className="text-sm font-mono uppercase tracking-wider text-white/40 mb-6">CORE FEATURES</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {mainFeatures.map((feature, index) => (
              <button
                key={index}
                onClick={() => navigate(feature.path)}
                className="group text-left p-6 rounded-lg bg-[#0A0A0C]/60 border border-white/[0.06] hover:border-emerald-500/30 hover:bg-[#0A0A0C]/80 transition-all duration-300"
              >
                <div className="flex items-start gap-4">
                  <div className="flex-shrink-0 w-10 h-10 rounded-lg bg-white/[0.04] group-hover:bg-emerald-500/10 border border-white/[0.08] group-hover:border-emerald-500/20 flex items-center justify-center transition-all duration-300">
                    <div className="text-white/70 group-hover:text-emerald-400 transition-colors duration-300">
                      {feature.icon}
                    </div>
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between mb-2">
                      <h3 className="font-semibold text-white/90 group-hover:text-white transition-colors duration-300">
                        {feature.title}
                      </h3>
                      <ChevronRight className="w-4 h-4 text-white/30 group-hover:text-emerald-400 group-hover:translate-x-1 transition-all duration-300" />
                    </div>
                    <p className="text-sm text-white/60 leading-relaxed">
                      {feature.description}
                    </p>
                  </div>
                </div>
              </button>
            ))}
          </div>
        </div>

        {/* Tools Section */}
        <div className="mb-16">
          <h2 className="text-sm font-mono uppercase tracking-wider text-white/40 mb-6">ADVANCED TOOLS</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {tools.map((tool, index) => (
              <button
                key={index}
                onClick={() => navigate(tool.path)}
                className="group text-left p-5 rounded-lg bg-[#0A0A0C]/40 border border-white/[0.04] hover:border-white/[0.12] hover:bg-[#0A0A0C]/60 transition-all duration-300"
              >
                <div className="flex items-center gap-3">
                  <div className="flex-shrink-0 w-8 h-8 rounded-md bg-white/[0.03] group-hover:bg-white/[0.06] border border-white/[0.06] flex items-center justify-center transition-all duration-300">
                    <div className="text-white/60 group-hover:text-white/80 transition-colors duration-300">
                      {tool.icon}
                    </div>
                  </div>
                  <div className="flex-1 min-w-0">
                    <h3 className="font-medium text-sm text-white/80 group-hover:text-white transition-colors duration-300 mb-1">
                      {tool.title}
                    </h3>
                    <p className="text-xs text-white/50 leading-relaxed">
                      {tool.description}
                    </p>
                  </div>
                  <ChevronRight className="w-3 h-3 text-white/20 group-hover:text-white/50 group-hover:translate-x-0.5 transition-all duration-300" />
                </div>
              </button>
            ))}
          </div>
        </div>

        {/* Quick Start */}
        <div className="p-6 rounded-lg bg-gradient-to-r from-emerald-500/[0.03] to-emerald-600/[0.02] border border-emerald-500/[0.08]">
          <div className="flex items-center gap-4">
            <div className="w-10 h-10 rounded-lg bg-emerald-500/10 border border-emerald-500/20 flex items-center justify-center">
              <MessageCircle className="w-5 h-5 text-emerald-400" />
            </div>
            <div className="flex-1">
              <h3 className="font-semibold text-white/90 mb-1">Start with AI Chat</h3>
              <p className="text-sm text-white/60">
                Get instant market insights and personalized trading analysis
              </p>
            </div>
            <button
              onClick={() => navigate('/chat')}
              className="px-4 py-2 rounded-md bg-emerald-500/10 hover:bg-emerald-500/20 border border-emerald-500/20 hover:border-emerald-500/30 text-emerald-400 text-sm font-medium transition-all duration-300"
            >
              Start Chat
            </button>
          </div>
        </div>

      </div>
    </div>
  );
};

export default Home;
