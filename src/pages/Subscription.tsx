import React, { useEffect } from 'react';
import { <PERSON>, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { CheckCircle, ArrowLeft } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Link, useNavigate } from 'react-router-dom';
import { SubscribeButton } from '@/components/subscription/SubscribeButton';
import { useSubscription } from '@/hooks/useSubscription';
import { useToast } from '@/components/ui/use-toast';

const Subscription = () => {
  const { toast } = useToast();
  const navigate = useNavigate();
  const { subscription, isLoadingSubscription, refetch } = useSubscription();

  return (
    <div className="h-full bg-[#0A0A0A] text-white flex flex-col font-hanken-grotesk">
      {/* Header Section */}
      <div className="px-8 py-6">
        <div className="flex items-center gap-3 mb-3">
          <h1 className="text-2xl font-bold text-white">Subscription</h1>
        </div>
        <p className="text-white/50 text-sm">
          Manage your subscription and billing preferences
        </p>
      </div>

      {/* Content */}
      <div className="flex-1 px-8 pb-6">
        <div className="bg-white/[0.02] border border-white/[0.06] rounded-lg p-6">
          {isLoadingSubscription ? (
            <div className="flex items-center justify-center py-12">
              <div className="flex flex-col items-center gap-4">
                <div className="h-8 w-8 rounded-full border-2 border-t-transparent border-white/30 animate-spin"></div>
                <p className="text-white/60 text-sm">Loading subscription information...</p>
              </div>
            </div>
          ) : (
            <div className="text-center py-12">
              <h2 className="text-lg font-medium text-white mb-2">Subscription Management</h2>
              <p className="text-white/60 text-sm">Your subscription details will appear here</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default Subscription;
