import React, { useState, useEffect } from 'react';
import { Save, User, Mail, Lock, ChevronDown } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { supabase } from '@/integrations/supabase/client';

const Settings = () => {
  const [fullName, setFullName] = useState('');
  const [email, setEmail] = useState('');
  const [loading, setLoading] = useState(false);
  const [user, setUser] = useState(null);
  const [activeSection, setActiveSection] = useState('profile');
  
  // Password change states
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [passwordError, setPasswordError] = useState('');
  const [showPasswordForm, setShowPasswordForm] = useState(false);

  useEffect(() => {
    const fetchUser = async () => {
      const { data: { user } } = await supabase.auth.getUser();
      if (user) {
        setUser(user);
        setEmail(user.email || '');
        
        // Fetch profile data if available
        const { data: profile } = await supabase
          .from('profiles')
          .select('full_name')
          .eq('id', user.id)
          .single();
          
        if (profile) {
          setFullName(profile.full_name || '');
        }
      }
    };

    fetchUser();
  }, []);

  const handleSaveChanges = async () => {
    setLoading(true);
    try {
      // Update profile in database
      const { error } = await supabase
        .from('profiles')
        .upsert({ 
          id: user.id, 
          full_name: fullName,
          updated_at: new Date()
        });

      if (error) throw error;
      
      // Show success message or notification here
    } catch (error) {
      console.error('Error updating profile:', error);
      // Show error message
    } finally {
      setLoading(false);
    }
  };
  
  const handlePasswordChange = async (e) => {
    e.preventDefault();
    setPasswordError('');
    
    if (newPassword !== confirmPassword) {
      setPasswordError("New passwords don't match");
      return;
    }
    
    if (newPassword.length < 6) {
      setPasswordError("Password must be at least 6 characters");
      return;
    }
    
    setLoading(true);
    try {
      const { error } = await supabase.auth.updateUser({ 
        password: newPassword 
      });
      
      if (error) throw error;
      
      // Reset form and show success
      setNewPassword('');
      setConfirmPassword('');
      setShowPasswordForm(false);
      // Show success message
    } catch (error) {
      console.error('Error changing password:', error);
      setPasswordError(error.message || "Failed to change password");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-[#0A0A0A]">
      <div className="max-w-[600px] mx-auto px-4 sm:px-6 pt-16 pb-20">
        <div className="text-center mb-12">
          <h1 className="text-2xl sm:text-3xl font-semibold text-white mb-2" style={{ fontFamily: 'SF Pro Display, -apple-system, BlinkMacSystemFont, sans-serif' }}>Account Settings</h1>
          <p className="text-white/60 text-sm sm:text-base" style={{ fontFamily: 'SF Pro Text, -apple-system, BlinkMacSystemFont, sans-serif' }}>Manage your profile and security preferences</p>
        </div>
        
        <div className="bg-[#111111] rounded-2xl border border-white/[0.08] overflow-hidden">
          <div className="px-5 py-4 border-b border-white/[0.08]">
            <button 
              onClick={() => setActiveSection(activeSection === 'profile' ? 'security' : 'profile')}
              className="w-full flex items-center justify-between text-left"
            >
              <div className="flex items-center gap-2">
                {activeSection === 'profile' ? (
                  <User className="h-4 w-4 text-white/60" />
                ) : (
                  <Lock className="h-4 w-4 text-white/60" />
                )}
                <h2 className="text-base font-medium text-white/90" style={{ fontFamily: 'SF Pro Display, -apple-system, BlinkMacSystemFont, sans-serif' }}>
                  {activeSection === 'profile' ? 'Profile Information' : 'Security'}
                </h2>
              </div>
              <ChevronDown className={`h-4 w-4 text-white/60 transition-transform duration-200 ${activeSection === 'security' ? 'rotate-180' : ''}`} />
            </button>
          </div>
          
          <div className="p-6">
            {activeSection === 'profile' && (
              <div className="space-y-6">
                <div className="space-y-2">
                  <label className="flex items-center gap-2 text-sm text-white/80" style={{ fontFamily: 'SF Pro Text, -apple-system, BlinkMacSystemFont, sans-serif' }}>
                    <User className="h-4 w-4 text-white/60" />
                    Full Name
                  </label>
                  <Input
                    type="text"
                    value={fullName}
                    onChange={(e) => setFullName(e.target.value)}
                    className="bg-[#0A0A0A] border-white/[0.08] h-11 rounded-lg text-white placeholder:text-white/40 focus:border-white/20 focus:ring-0 transition-colors duration-200"
                    style={{ fontFamily: 'SF Pro Text, -apple-system, BlinkMacSystemFont, sans-serif' }}
                    placeholder="Enter your full name"
                  />
                </div>
                
                <div className="space-y-2">
                  <label className="flex items-center gap-2 text-sm text-white/80" style={{ fontFamily: 'SF Pro Text, -apple-system, BlinkMacSystemFont, sans-serif' }}>
                    <Mail className="h-4 w-4 text-white/60" />
                    Email Address
                  </label>
                  <Input
                    type="email"
                    value={email}
                    disabled
                    className="bg-[#0A0A0A] border-white/[0.08] h-11 rounded-lg text-white/60 cursor-not-allowed"
                    style={{ fontFamily: 'SF Pro Text, -apple-system, BlinkMacSystemFont, sans-serif' }}
                  />
                  <p className="text-xs text-white/40" style={{ fontFamily: 'SF Pro Text, -apple-system, BlinkMacSystemFont, sans-serif' }}>
                    Email cannot be changed
                  </p>
                </div>
                
                <button
                  onClick={handleSaveChanges}
                  disabled={loading}
                  className="w-full rounded-lg h-11 text-sm bg-white/[0.03] hover:bg-white/[0.06] text-white/90 border border-white/[0.08] flex items-center justify-center gap-2 transition-colors duration-200"
                  style={{ fontFamily: 'SF Pro Text, -apple-system, BlinkMacSystemFont, sans-serif' }}
                >
                  <Save className="h-4 w-4" />
                  {loading ? 'Saving...' : 'Save Changes'}
                </button>
              </div>
            )}
            
            {activeSection === 'security' && (
              <div>
                {!showPasswordForm ? (
                  <>
                    <p className="text-white/60 text-sm mb-6" style={{ fontFamily: 'SF Pro Text, -apple-system, BlinkMacSystemFont, sans-serif' }}>
                      Manage your account security settings and password
                    </p>
                    
                    <button
                      onClick={() => setShowPasswordForm(true)}
                      className="w-full rounded-lg h-11 text-sm bg-white/[0.03] hover:bg-white/[0.06] text-white/90 border border-white/[0.08] flex items-center justify-center gap-2 transition-colors duration-200"
                      style={{ fontFamily: 'SF Pro Text, -apple-system, BlinkMacSystemFont, sans-serif' }}
                    >
                      <Lock className="h-4 w-4" />
                      Change Password
                    </button>
                  </>
                ) : (
                  <form onSubmit={handlePasswordChange} className="space-y-5">
                    <div className="space-y-2">
                      <label className="text-sm text-white/80" style={{ fontFamily: 'SF Pro Text, -apple-system, BlinkMacSystemFont, sans-serif' }}>
                        New Password
                      </label>
                      <Input
                        type="password"
                        value={newPassword}
                        onChange={(e) => setNewPassword(e.target.value)}
                        className="bg-[#0A0A0A] border-white/[0.08] h-11 rounded-lg text-white placeholder:text-white/40 focus:border-white/20 focus:ring-0 transition-colors duration-200"
                        style={{ fontFamily: 'SF Pro Text, -apple-system, BlinkMacSystemFont, sans-serif' }}
                        placeholder="Enter new password"
                        required
                      />
                    </div>
                    
                    <div className="space-y-2">
                      <label className="text-sm text-white/80" style={{ fontFamily: 'SF Pro Text, -apple-system, BlinkMacSystemFont, sans-serif' }}>
                        Confirm New Password
                      </label>
                      <Input
                        type="password"
                        value={confirmPassword}
                        onChange={(e) => setConfirmPassword(e.target.value)}
                        className="bg-[#0A0A0A] border-white/[0.08] h-11 rounded-lg text-white placeholder:text-white/40 focus:border-white/20 focus:ring-0 transition-colors duration-200"
                        style={{ fontFamily: 'SF Pro Text, -apple-system, BlinkMacSystemFont, sans-serif' }}
                        placeholder="Confirm new password"
                        required
                      />
                    </div>
                    
                    {passwordError && (
                      <p className="text-red-500/90 text-sm" style={{ fontFamily: 'SF Pro Text, -apple-system, BlinkMacSystemFont, sans-serif' }}>
                        {passwordError}
                      </p>
                    )}
                    
                    <div className="flex gap-3 pt-1">
                      <button
                        type="button"
                        onClick={() => {
                          setShowPasswordForm(false);
                          setPasswordError('');
                          setNewPassword('');
                          setConfirmPassword('');
                        }}
                        className="flex-1 rounded-lg h-11 text-sm bg-white/[0.03] hover:bg-white/[0.06] text-white/90 border border-white/[0.08] transition-colors duration-200"
                        style={{ fontFamily: 'SF Pro Text, -apple-system, BlinkMacSystemFont, sans-serif' }}
                      >
                        Cancel
                      </button>
                      
                      <button
                        type="submit"
                        disabled={loading}
                        className="flex-1 rounded-lg h-11 text-sm bg-white/[0.03] hover:bg-white/[0.06] text-white/90 border border-white/[0.08] flex items-center justify-center gap-2 transition-colors duration-200"
                        style={{ fontFamily: 'SF Pro Text, -apple-system, BlinkMacSystemFont, sans-serif' }}
                      >
                        {loading ? 'Updating...' : 'Update Password'}
                      </button>
                    </div>
                  </form>
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Settings;
