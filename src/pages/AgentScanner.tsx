import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Loader2, Search, TrendingUp, AlertCircle, ArrowUpRight, ArrowDownRight } from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';
import { useAuth } from '@/contexts/AuthContext';
import { getAgentsByUserId } from '@/services/agentService';
import { supabase } from '@/integrations/supabase/client';

interface Agent {
  id: string;
  name: string;
  description: string;
  configuration: any;
}

interface ScanResult {
  symbol: string;
  signal: string;
  confidence: number;
  price: number;
  change: number;
  percentChange: number;
}

const AgentScanner: React.FC = () => {
  const { user } = useAuth();
  const { toast } = useToast();

  const [agents, setAgents] = useState<Agent[]>([]);
  const [selectedAgent, setSelectedAgent] = useState<string>('');
  const [selectedIndex, setSelectedIndex] = useState<string>('sp500');
  const [isScanning, setIsScanning] = useState(false);
  const [scanResults, setScanResults] = useState<ScanResult[]>([]);
  const [loadingAgents, setLoadingAgents] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');

  // Market indices options
  const marketIndices = [
    { value: 'sp500', label: 'S&P 500' },
    { value: 'nasdaq', label: 'NASDAQ Composite' },
    { value: 'nasdaq100', label: 'NASDAQ 100' },
    { value: 'russell2000', label: 'Russell 2000' },
    { value: 'all', label: 'All Stocks' }
  ];

  // Load user's agents
  useEffect(() => {
    const loadAgents = async () => {
      if (!user?.id) return;

      try {
        const userAgents = await getAgentsByUserId(user.id);
        setAgents(userAgents);
      } catch (error) {
        console.error('Error loading agents:', error);
        toast({
          title: 'Error',
          description: 'Failed to load your agents',
          variant: 'destructive'
        });
      } finally {
        setLoadingAgents(false);
      }
    };

    loadAgents();
  }, [user?.id, toast]);

  // Run agent scanner
  const handleScan = async () => {
    if (!selectedAgent || !selectedIndex) {
      toast({
        title: 'Missing Selection',
        description: 'Please select both an agent and market index',
        variant: 'destructive'
      });
      return;
    }

    setIsScanning(true);
    setScanResults([]);

    try {
      // Call the agent scanner edge function
      const { data, error } = await supabase.functions.invoke('agent-scanner', {
        body: {
          agentId: selectedAgent,
          marketIndex: selectedIndex,
          userId: user?.id
        }
      });

      if (error) {
        throw error;
      }

      setScanResults(data.results || []);

      toast({
        title: 'Scan Complete',
        description: `Found ${data.results?.length || 0} bullish signals`,
      });
    } catch (error) {
      console.error('Error running agent scanner:', error);
      toast({
        title: 'Scan Failed',
        description: 'Failed to run agent scanner. Please try again.',
        variant: 'destructive'
      });
    } finally {
      setIsScanning(false);
    }
  };

  // Filter results based on search query
  const filteredResults = scanResults.filter(result =>
    result.symbol.toLowerCase().includes(searchQuery.toLowerCase())
  );

  if (loadingAgents) {
    return (
      <div className="h-full bg-[#0A0A0A] text-white flex items-center justify-center">
        <div className="flex flex-col items-center gap-4">
          <Loader2 className="h-8 w-8 animate-spin text-white/60" />
          <p className="text-white/60 text-sm">Loading your agents...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full bg-[#0A0A0A] text-white flex flex-col font-hanken-grotesk">
      {/* Header Section */}
      <div className="px-8 py-6">
        <div className="flex items-center gap-3 mb-3">
          <Search className="h-5 w-5 text-white/60" />
          <h1 className="text-xl font-medium text-white">Agent Scanner</h1>
        </div>
        <p className="text-white/50 text-sm">
          Run your trading agents against market data to find stocks that match your criteria
        </p>
      </div>

      {/* Scanner Configuration */}
      <div className="px-8 pb-6">
        <div className="bg-white/[0.02] border border-white/[0.06] rounded-lg p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-3">
              <label className="text-sm font-medium text-white/80">Select Agent</label>
              <Select value={selectedAgent} onValueChange={setSelectedAgent}>
                <SelectTrigger className="bg-white/[0.02] border-white/[0.06] text-white h-10 text-sm">
                  <SelectValue placeholder="Choose an agent to run" />
                </SelectTrigger>
                <SelectContent className="bg-[#1A1A1A] border-white/[0.08]">
                  {agents.map((agent) => (
                    <SelectItem key={agent.id} value={agent.id} className="text-white hover:bg-white/[0.05] text-sm">
                      {agent.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {agents.length === 0 && (
                <p className="text-xs text-white/50">
                  No agents found. Create an agent first in the Agent Builder.
                </p>
              )}
            </div>

            <div className="space-y-3">
              <label className="text-sm font-medium text-white/80">Market Scope</label>
              <Select value={selectedIndex} onValueChange={setSelectedIndex}>
                <SelectTrigger className="bg-white/[0.02] border-white/[0.06] text-white h-10 text-sm">
                  <SelectValue placeholder="Choose market index" />
                </SelectTrigger>
                <SelectContent className="bg-[#1A1A1A] border-white/[0.08]">
                  {marketIndices.map((index) => (
                    <SelectItem key={index.value} value={index.value} className="text-white hover:bg-white/[0.05] text-sm">
                      {index.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="flex justify-center pt-6">
            <Button
              onClick={handleScan}
              disabled={!selectedAgent || !selectedIndex || isScanning}
              className="bg-white/[0.06] hover:bg-white/[0.08] border border-white/[0.05] text-white/80 hover:text-white shadow-[inset_0_2px_6px_rgba(0,0,0,0.15),inset_0_1px_0_rgba(255,255,255,0.1)] px-6 py-2 text-sm font-medium"
            >
              {isScanning ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Scanning...
                </>
              ) : (
                <>
                  <Search className="mr-2 h-4 w-4" />
                  Run Scanner
                </>
              )}
            </Button>
          </div>
        </div>
      </div>

      {/* Scan Results */}
      {scanResults.length > 0 && (
        <div className="flex-1 px-8 pb-6">
          <div className="bg-white/[0.02] border border-white/[0.06] rounded-lg h-full flex flex-col">
            <div className="p-6 pb-4 border-b border-white/[0.06]">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <TrendingUp className="h-4 w-4 text-white/60" />
                  <h2 className="text-sm font-medium text-white">Bullish Signals Found ({scanResults.length})</h2>
                </div>
                {scanResults.length > 5 && (
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-3 w-3 text-white/40" />
                    <input
                      type="text"
                      placeholder="Search stocks..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="pl-9 pr-3 py-1.5 bg-white/[0.02] border border-white/[0.06] rounded text-white placeholder-white/40 focus:border-white/[0.12] focus:outline-none text-xs w-48"
                    />
                  </div>
                )}
              </div>
            </div>
            <div className="flex-1 overflow-y-auto p-6">
              <div className="space-y-2">
                {filteredResults.map((result, index) => (
                  <div
                    key={index}
                    className="bg-white/[0.02] border border-white/[0.04] hover:bg-white/[0.03] hover:border-white/[0.06] transition-all duration-200 rounded p-4 border-l-2 border-l-emerald-500/40"
                  >
                    <div className="flex items-center justify-between">
                      {/* Left side - Stock info */}
                      <div className="flex items-center gap-3">
                        <div className="flex flex-col">
                          <div className="flex items-center gap-2">
                            <h3 className="font-medium text-white text-sm">{result.symbol}</h3>
                            <span className="bg-white/[0.06] text-white/70 border border-white/[0.08] text-xs px-2 py-0.5 rounded">
                              {result.confidence}%
                            </span>
                          </div>
                          <p className="text-white/50 text-xs mt-0.5">{result.signal}</p>
                        </div>
                      </div>

                      {/* Right side - Price and change */}
                      <div className="text-right">
                        <div className="text-sm font-medium text-white mb-0.5">
                          ${result.price.toFixed(2)}
                        </div>
                        <div className={`flex items-center justify-end gap-1 text-xs ${
                          result.change >= 0 ? 'text-emerald-400' : 'text-red-400'
                        }`}>
                          {result.change >= 0 ? (
                            <ArrowUpRight className="h-2.5 w-2.5" />
                          ) : (
                            <ArrowDownRight className="h-2.5 w-2.5" />
                          )}
                          {result.change >= 0 ? '+' : ''}${result.change.toFixed(2)} ({result.percentChange.toFixed(2)}%)
                        </div>
                      </div>
                    </div>
                  </div>
                ))}

                {filteredResults.length === 0 && searchQuery && (
                  <div className="text-center py-12">
                    <Search className="h-8 w-8 text-white/20 mx-auto mb-3" />
                    <h3 className="text-white/60 font-medium mb-1 text-sm">No stocks found</h3>
                    <p className="text-white/40 text-xs">
                      No stocks match your search for "{searchQuery}"
                    </p>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* No Results Message */}
      {!isScanning && scanResults.length === 0 && selectedAgent && selectedIndex && (
        <div className="flex-1 flex items-center justify-center px-8 pb-6">
          <div className="bg-white/[0.02] border border-white/[0.06] rounded-lg max-w-md w-full p-8 text-center">
            <AlertCircle className="h-8 w-8 text-white/40 mx-auto mb-4" />
            <h3 className="text-sm font-medium mb-2 text-white">No Signals Found</h3>
            <p className="text-white/50 text-xs">
              Your agent didn't find any bullish signals in the selected market index.
              Try adjusting your agent's criteria or selecting a different market scope.
            </p>
          </div>
        </div>
      )}
    </div>
  );
};

export default AgentScanner;
