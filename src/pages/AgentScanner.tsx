import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Loader2, Search, TrendingUp, AlertCircle, ArrowUpRight, ArrowDownRight } from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';
import { useAuth } from '@/contexts/AuthContext';
import { getAgentsByUserId } from '@/services/agentService';
import { supabase } from '@/integrations/supabase/client';

interface Agent {
  id: string;
  name: string;
  description: string;
  configuration: any;
}

interface ScanResult {
  symbol: string;
  signal: string;
  confidence: number;
  price: number;
  change: number;
  percentChange: number;
}

const AgentScanner: React.FC = () => {
  const { user } = useAuth();
  const { toast } = useToast();

  const [agents, setAgents] = useState<Agent[]>([]);
  const [selectedAgent, setSelectedAgent] = useState<string>('');
  const [selectedIndex, setSelectedIndex] = useState<string>('sp500');
  const [isScanning, setIsScanning] = useState(false);
  const [scanResults, setScanResults] = useState<ScanResult[]>([]);
  const [loadingAgents, setLoadingAgents] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');

  // Market indices options with logos
  const marketIndices = [
    {
      value: 'sp500',
      label: 'S&P 500',
      logo: '🏛️',
      description: '500 largest US companies'
    },
    {
      value: 'nasdaq',
      label: 'NASDAQ Composite',
      logo: '💻',
      description: 'Tech-heavy index'
    },
    {
      value: 'nasdaq100',
      label: 'NASDAQ 100',
      logo: '🚀',
      description: 'Top 100 non-financial NASDAQ stocks'
    },
    {
      value: 'russell2000',
      label: 'Russell 2000',
      logo: '🏢',
      description: 'Small-cap companies'
    },
    {
      value: 'all',
      label: 'All Stocks',
      logo: '🌐',
      description: 'Entire market universe'
    }
  ];

  // Load user's agents
  useEffect(() => {
    const loadAgents = async () => {
      if (!user?.id) return;

      try {
        const userAgents = await getAgentsByUserId(user.id);
        setAgents(userAgents);
      } catch (error) {
        console.error('Error loading agents:', error);
        toast({
          title: 'Error',
          description: 'Failed to load your agents',
          variant: 'destructive'
        });
      } finally {
        setLoadingAgents(false);
      }
    };

    loadAgents();
  }, [user?.id, toast]);

  // Run agent scanner
  const handleScan = async () => {
    if (!selectedAgent || !selectedIndex) {
      toast({
        title: 'Missing Selection',
        description: 'Please select both an agent and market index',
        variant: 'destructive'
      });
      return;
    }

    setIsScanning(true);
    setScanResults([]);

    try {
      // Call the agent scanner edge function
      const { data, error } = await supabase.functions.invoke('agent-scanner', {
        body: {
          agentId: selectedAgent,
          marketIndex: selectedIndex,
          userId: user?.id
        }
      });

      if (error) {
        throw error;
      }

      setScanResults(data.results || []);

      toast({
        title: 'Scan Complete',
        description: `Found ${data.results?.length || 0} bullish signals`,
      });
    } catch (error) {
      console.error('Error running agent scanner:', error);
      toast({
        title: 'Scan Failed',
        description: 'Failed to run agent scanner. Please try again.',
        variant: 'destructive'
      });
    } finally {
      setIsScanning(false);
    }
  };

  // Filter results based on search query
  const filteredResults = scanResults.filter(result =>
    result.symbol.toLowerCase().includes(searchQuery.toLowerCase())
  );

  if (loadingAgents) {
    return (
      <div className="h-full bg-[#0A0A0A] text-white flex items-center justify-center">
        <div className="flex flex-col items-center gap-4">
          <Loader2 className="h-8 w-8 animate-spin text-white/60" />
          <p className="text-white/60 text-sm">Loading your agents...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full bg-[#141414] text-white flex flex-col font-hanken-grotesk">
      {/* Compact Header */}
      <div className="px-8 py-6 border-b border-white/[0.06]">
        <div className="max-w-7xl mx-auto">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 rounded-lg bg-gradient-to-br from-emerald-500/20 to-emerald-600/20 border border-emerald-500/30 flex items-center justify-center">
                <Target className="h-5 w-5 text-emerald-400" />
              </div>
              <div>
                <h1 className="text-2xl font-bold text-white">Agent Scanner</h1>
                <p className="text-white/60 text-sm">Deploy AI agents to find trading opportunities</p>
              </div>
            </div>

            {/* Compact Stats */}
            <div className="flex items-center gap-8">
              <div className="text-center">
                <p className="text-xl font-bold text-emerald-400">{agents.length}</p>
                <p className="text-xs text-white/60">Agents</p>
              </div>
              <div className="text-center">
                <p className="text-xl font-bold text-blue-400">{scanResults.length}</p>
                <p className="text-xs text-white/60">Signals</p>
              </div>
              <div className="text-center">
                <p className="text-xl font-bold text-purple-400">
                  {scanResults.length > 0 ? Math.round((scanResults.filter(r => r.confidence > 70).length / scanResults.length) * 100) : 0}%
                </p>
                <p className="text-xs text-white/60">Success</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Compact Configuration */}
      <div className="px-8 py-6">
        <div className="max-w-7xl mx-auto">
          <div className="bg-[#0D0D0D]/80 backdrop-blur-xl border border-white/[0.08] rounded-xl p-6 shadow-[0_8px_32px_rgba(0,0,0,0.4)]">
            <div className="flex items-center gap-8">
              {/* Agent Selection */}
              <div className="flex-1">
                <label className="text-sm font-medium text-white/90 mb-2 block">Trading Agent</label>
                <Select value={selectedAgent} onValueChange={setSelectedAgent}>
                  <SelectTrigger className="bg-[#141414]/60 border-white/[0.08] text-white h-11 rounded-lg hover:border-white/[0.12] transition-colors">
                    <SelectValue placeholder="Choose your AI agent" />
                  </SelectTrigger>
                  <SelectContent className="bg-[#1A1A1A] border-white/[0.08] rounded-lg">
                    {agents.map((agent) => (
                      <SelectItem key={agent.id} value={agent.id} className="text-white hover:bg-white/[0.05] rounded-md">
                        <div className="flex items-center gap-2">
                          <div className="w-2 h-2 rounded-full bg-emerald-500"></div>
                          {agent.name}
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {agents.length === 0 && (
                  <p className="text-xs text-amber-400 mt-2 flex items-center gap-1">
                    <AlertCircle className="h-3 w-3" />
                    No agents found. Create one in Agent Builder.
                  </p>
                )}
              </div>

              {/* Market Selection */}
              <div className="flex-1">
                <label className="text-sm font-medium text-white/90 mb-2 block">Market Universe</label>
                <Select value={selectedIndex} onValueChange={setSelectedIndex}>
                  <SelectTrigger className="bg-[#141414]/60 border-white/[0.08] text-white h-11 rounded-lg hover:border-white/[0.12] transition-colors">
                    <SelectValue placeholder="Choose market scope" />
                  </SelectTrigger>
                  <SelectContent className="bg-[#1A1A1A] border-white/[0.08] rounded-lg">
                    {marketIndices.map((index) => (
                      <SelectItem key={index.value} value={index.value} className="text-white hover:bg-white/[0.05] rounded-md">
                        <div className="flex items-center gap-3">
                          <span className="text-lg">{index.logo}</span>
                          <div>
                            <div className="font-medium">{index.label}</div>
                            <div className="text-xs text-white/60">{index.description}</div>
                          </div>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Deploy Button */}
              <div className="flex-shrink-0">
                <label className="text-sm font-medium text-white/90 mb-2 block opacity-0">Action</label>
                <Button
                  onClick={handleScan}
                  disabled={!selectedAgent || !selectedIndex || isScanning}
                  className="bg-gradient-to-r from-emerald-500/20 to-emerald-600/20 hover:from-emerald-500/30 hover:to-emerald-600/30 border border-emerald-500/30 text-emerald-400 hover:text-emerald-300 h-11 px-6 rounded-lg transition-all duration-300 group"
                >
                  <span className="flex items-center gap-2">
                    {isScanning ? (
                      <>
                        <Loader2 className="h-4 w-4 animate-spin" />
                        Scanning...
                      </>
                    ) : (
                      <>
                        <Target className="h-4 w-4" />
                        Deploy
                        <ChevronRight className="h-4 w-4 group-hover:translate-x-1 transition-transform duration-300" />
                      </>
                    )}
                  </span>
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Clean Results Table */}
      {scanResults.length > 0 && (
        <div className="flex-1 px-8 pb-8">
          <div className="max-w-7xl mx-auto">
            <div className="bg-[#0D0D0D]/80 backdrop-blur-xl border border-white/[0.08] rounded-xl shadow-[0_8px_32px_rgba(0,0,0,0.4)]">
              {/* Header */}
              <div className="p-6 border-b border-white/[0.08]">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 rounded-lg bg-emerald-500/10 flex items-center justify-center">
                      <TrendingUp className="h-4 w-4 text-emerald-400" />
                    </div>
                    <div>
                      <h2 className="text-lg font-semibold text-white">Trading Opportunities</h2>
                      <p className="text-xs text-white/60">{scanResults.length} bullish signals detected</p>
                    </div>
                  </div>
                  {scanResults.length > 5 && (
                    <div className="relative">
                      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-white/40" />
                      <input
                        type="text"
                        placeholder="Filter symbols..."
                        value={searchQuery}
                        onChange={(e) => setSearchQuery(e.target.value)}
                        className="pl-10 pr-4 py-2 bg-[#141414]/60 border border-white/[0.08] rounded-lg text-white placeholder-white/40 focus:border-white/[0.12] focus:outline-none text-sm w-56 transition-colors"
                      />
                    </div>
                  )}
                </div>
              </div>

              {/* Table Header */}
              <div className="px-6 py-3 border-b border-white/[0.06] bg-white/[0.02]">
                <div className="grid grid-cols-12 gap-4 text-xs font-medium text-white/60 uppercase tracking-wider">
                  <div className="col-span-3">Symbol</div>
                  <div className="col-span-4">Signal</div>
                  <div className="col-span-2 text-center">Confidence</div>
                  <div className="col-span-2 text-right">Price</div>
                  <div className="col-span-1 text-right">Change</div>
                </div>
              </div>

              {/* Results */}
              <div className="max-h-96 overflow-y-auto">
                {filteredResults.map((result, index) => (
                  <div
                    key={index}
                    className="px-6 py-4 border-b border-white/[0.04] hover:bg-white/[0.02] transition-colors duration-200 group"
                  >
                    <div className="grid grid-cols-12 gap-4 items-center">
                      {/* Symbol */}
                      <div className="col-span-3">
                        <div className="flex items-center gap-3">
                          <div className="w-8 h-8 rounded-lg bg-emerald-500/10 border border-emerald-500/20 flex items-center justify-center">
                            <span className="text-emerald-400 font-semibold text-xs">{result.symbol.slice(0, 2)}</span>
                          </div>
                          <span className="font-semibold text-white">{result.symbol}</span>
                        </div>
                      </div>

                      {/* Signal */}
                      <div className="col-span-4">
                        <span className="text-white/80 text-sm">{result.signal}</span>
                      </div>

                      {/* Confidence */}
                      <div className="col-span-2 text-center">
                        <div className="inline-flex items-center gap-1">
                          <div className="w-2 h-2 rounded-full bg-emerald-500 animate-pulse"></div>
                          <span className="text-emerald-400 font-medium text-sm">{result.confidence}%</span>
                        </div>
                      </div>

                      {/* Price */}
                      <div className="col-span-2 text-right">
                        <span className="text-white font-semibold">${result.price.toFixed(2)}</span>
                      </div>

                      {/* Change */}
                      <div className="col-span-1 text-right">
                        <div className={`flex items-center justify-end gap-1 text-sm font-medium ${
                          result.change >= 0 ? 'text-emerald-400' : 'text-red-400'
                        }`}>
                          {result.change >= 0 ? (
                            <ArrowUpRight className="h-3 w-3" />
                          ) : (
                            <ArrowDownRight className="h-3 w-3" />
                          )}
                          <span>{result.percentChange.toFixed(1)}%</span>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}

                {filteredResults.length === 0 && searchQuery && (
                  <div className="text-center py-16">
                    <div className="w-16 h-16 rounded-full bg-white/[0.02] border border-white/[0.06] flex items-center justify-center mx-auto mb-4">
                      <Search className="h-6 w-6 text-white/30" />
                    </div>
                    <h3 className="text-white/70 font-medium mb-2">No matches found</h3>
                    <p className="text-white/50 text-sm">
                      No stocks match your search for "{searchQuery}"
                    </p>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* No Results Message */}
      {!isScanning && scanResults.length === 0 && selectedAgent && selectedIndex && (
        <div className="flex-1 flex items-center justify-center px-8 pb-6">
          <div className="bg-white/[0.02] border border-white/[0.06] rounded-lg max-w-md w-full p-8 text-center">
            <AlertCircle className="h-8 w-8 text-white/40 mx-auto mb-4" />
            <h3 className="text-sm font-medium mb-2 text-white">No Signals Found</h3>
            <p className="text-white/50 text-xs">
              Your agent didn't find any bullish signals in the selected market index.
              Try adjusting your agent's criteria or selecting a different market scope.
            </p>
          </div>
        </div>
      )}
    </div>
  );
};

export default AgentScanner;
