import { useState, useEffect } from 'react';
import { MessageSquare } from 'lucide-react';
import { useSubscription, SUBSCRIPTION_TYPES } from '@/hooks/useSubscription';
import { useUserLimits } from '@/hooks/useUserLimits';
import { useToast } from '@/hooks/use-toast';
import { useLocation, useNavigate } from 'react-router-dom';
import { Progress } from '@/components/ui/progress';
import { supabase } from '@/integrations/supabase/client';
import { useWhop } from '@/contexts/WhopContext';
import { motion } from 'framer-motion';
import StockPriceDisplay from '@/components/StockPriceDisplay';
import { AdvancedStockChart } from '@/components/charts/AdvancedStockChart';
import LoadingMessage from '@/components/chat/LoadingMessage';
import { loadStripe } from '@stripe/stripe-js';

// Paywall pricing configuration
const PAYWALL_VERSIONS = {
  paywall1: {
    weekly: 5.99
  },
  pro: {
    weekly: 9.99
  }
};

// Plans configuration with consistent IDs
const plans = [
  {
    id: {
      weekly: 'price_1ROYLKDebmd1GpTvct491Kw6'
    },
    name: 'Basic Plan',
    type: SUBSCRIPTION_TYPES.basic,
    price: {
      weekly: PAYWALL_VERSIONS.paywall1.weekly
    },
    period: 'per week',
    description: 'Entry level plan',
    features: [
      'All 7 AI Agents',
      'Stock and Crypto Analysis',
      'Instant Trading Information',
      '100 messages per month',
      '5 year Portfolio Backtesting',
      'Create 1 Portfolio'
    ]
  },
  {
    id: {
      weekly: 'price_1ROYKjDebmd1GpTv5oYNMKMv'
    },
    name: 'Investor',
    type: SUBSCRIPTION_TYPES.pro,
    price: {
      weekly: PAYWALL_VERSIONS.pro.weekly
    },
    period: 'per week',
    description: 'Everything you need to start investing',
    features: [
      'All 7 AI Agents',
      'Stock and Crypto Analysis',
      'Instant Trading Information',
      '200 messages per month',
      '20 year Portfolio Backtesting',
      'Create 3+ Portfolios'
    ]
  }
];

// Helper to map plan types to display names
const planTypeToDisplayName = {
  [SUBSCRIPTION_TYPES.basic]: 'Basic',
  [SUBSCRIPTION_TYPES.pro]: 'Investor'
};

export default function ManageSubscription() {
  const { subscription, isLoadingSubscription, refetch } = useSubscription();
  const { messagesUsed, messagesLimit, messagesRemaining, isLoading: isLoadingLimits, handlePlanUpgrade } = useUserLimits();
  const { isWhopUser, whopSubscription } = useWhop();
  const [selectedPlan, setSelectedPlan] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();
  const location = useLocation();
  const navigate = useNavigate();
  const [isAnnualPlan, setIsAnnualPlan] = useState(false);
  const [symbol, setSymbol] = useState('');
  const [showAIAnalysis, setShowAIAnalysis] = useState(false);
  const [analysisResults, setAnalysisResults] = useState([]);
  const [loading, setLoading] = useState(false);
  const [tradingStrategy, setTradingStrategy] = useState<{
    direction: 'LONG' | 'SHORT';
    confidence: 'HIGH' | 'MEDIUM' | 'LOW';
    entryPoints: Array<{ price: number; timestamp: number; label: string }>;
    exitPoints: Array<{ price: number; timestamp: number; label: string }>;
    stopLoss: number;
    supportLevels: number[];
    resistanceLevels: number[];
    takeProfitTargets: Array<{ price: number; timestamp: number; label: string }>;
    riskRewardRatio: string;
  }>({
    direction: 'LONG',
    confidence: 'MEDIUM',
    entryPoints: [],
    exitPoints: [],
    stopLoss: 0,
    supportLevels: [],
    resistanceLevels: [],
    takeProfitTargets: [],
    riskRewardRatio: '1:1'
  });
  const [symbolTypes, setSymbolTypes] = useState({});

  // Set initial annual/monthly state based on subscription
  useEffect(() => {
    if (subscription?.interval === 'year') {
      setIsAnnualPlan(true);
    } else {
      setIsAnnualPlan(false);
    }
  }, [subscription]);

  // Handle Stripe redirect with query parameters
  useEffect(() => {
    // Skip if we're a Whop user
    if (isWhopUser) return;

    const handleStripeRedirect = async () => {
      // Create a URL object to easily parse query parameters
      const url = new URL(window.location.href);
      const sessionId = url.searchParams.get('session_id');
      const success = url.searchParams.get('success');
      const customerId = url.searchParams.get('customer_id');

      // Only process if we have a session ID
      if (sessionId) {
        setIsLoading(true);

        try {
          // Handle the checkout redirect
          const response = await fetch(`${import.meta.env.VITE_SUPABASE_URL}/functions/v1/stripe`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${(await supabase.auth.getSession()).data.session?.access_token}`
            },
            body: JSON.stringify({
              action: 'handle-checkout-redirect',
              sessionId,
              customerId
            })
          });

          const { session, error } = await response.json();

          if (error) {
            throw new Error(error);
          }

          if (session?.status === 'complete') {
            toast({
              title: "Subscription Updated",
              description: "Your subscription has been updated successfully.",
            });

            // Check if user profile exists
            const { data: { session: authSession } } = await supabase.auth.getSession();
            if (authSession?.user) {
              const { data: profileExists, error: profileCheckError } = await supabase
                .from('profiles')
                .select('id')
                .eq('id', authSession.user.id)
                .maybeSingle();

              // If profile doesn't exist, create it
              if (!profileExists) {
                const { error: createError } = await supabase
                  .from('profiles')
                  .insert({
                    id: authSession.user.id,
                    subscription_type: null,
                    has_seen_onboarding: false,
                    created_at: new Date().toISOString(),
                    updated_at: new Date().toISOString()
                  });

                if (createError) {
                  console.error('Error creating profile:', createError);
                }
              }
            }

            // Refresh the onboarding status to hide the onboarding component
            // @ts-ignore - Accessing the function we added to window
            if (window.refreshOnboardingStatus) {
              try {
                // @ts-ignore
                window.refreshOnboardingStatus();
              } catch (error) {
                console.error('Error calling refreshOnboardingStatus:', error);
              }
            }
          }

          // Refresh subscription data and limits
          await refetch();
          await handlePlanUpgrade();

          // Remove query parameters from URL
          navigate('/subscription/manage', { replace: true });
        } catch (error) {
          console.error('Error handling Stripe redirect:', error);
          toast({
            title: "Update Failed",
            description: "There was an error processing your subscription update. Please try again.",
            variant: "destructive",
          });
        } finally {
          setIsLoading(false);
        }
      }
    };

    handleStripeRedirect();
  }, [location, navigate, toast, setIsLoading, refetch, handlePlanUpgrade, isWhopUser]);

  // If subscription or limits data changes, update the UI
  useEffect(() => {
    // Skip if we're a Whop user
    if (isWhopUser) return;

    if (!subscription || isLoadingSubscription || isLoadingLimits) return;

    // Use subscription_type to determine selected plan
    if (subscription.subscription_type) {
      // Find plan by type
      const plan = plans.find(p => p.type === subscription.subscription_type);
      if (plan) {
        setSelectedPlan(plan.id.weekly);
        return;
      }
    }

    // Fallback to free plan
    setSelectedPlan('price_free');
  }, [subscription, isLoadingSubscription, isLoadingLimits, isWhopUser, plans]);

  // Refresh data function
  const refreshData = async () => {
    setIsLoading(true);

    try {
      // Refresh subscription and limits data
      await refetch();
      // Use handlePlanUpgrade for a more thorough refresh with forced reset
      await handlePlanUpgrade();

      toast({
        title: "Data Refreshed",
        description: "Your subscription information has been updated.",
      });
    } catch (error) {
      console.error('Error refreshing data:', error);
      toast({
        title: "Refresh Failed",
        description: "Could not refresh your subscription data. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Get current plan display name
  const getCurrentPlanName = () => {
    if (isWhopUser) {
      // Return plan name based on Whop subscription
      return whopSubscription?.plan?.name || 'Free';
    }

    if (!subscription || isLoadingSubscription) return 'Free';

    // Use subscription_type if available
    if (subscription.subscription_type) {
      return planTypeToDisplayName[subscription.subscription_type] || 'Free';
    }

    return 'Free';
  };

  // Handle plan selection and update
  const handleSelectPlan = async (planType: string) => {
    // If using Whop, redirect to Whop's subscription management
    if (isWhopUser) {
      window.location.href = 'https://whop.com/hub';
      return;
    }

    // Don't do anything if this is already the current plan
    if (isCurrentPlan(planType)) {
      return;
    }

    setIsLoading(true);

    try {
      const planToSelect = plans.find(p => p.type === planType);
      if (!planToSelect) {
        throw new Error("Invalid plan selected");
      }

      // For any paid plan selection, use Stripe checkout
      if (planToSelect.name !== 'Free') {
        try {
          const actionToUse = getCurrentPlanName() === 'Free' ? 'create-checkout-session' : 'create-checkout-session';

          // Check for no-trial parameters in the URL
          const urlParams = new URLSearchParams(window.location.search);
          const basicNoTrial = urlParams.get('basicnotrial') !== null;
          const proNoTrial = urlParams.get('pronotrial') !== null;
          const skipTrial = true;

          const response = await fetch(`${import.meta.env.VITE_SUPABASE_URL}/functions/v1/stripe-checkout`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${(await supabase.auth.getSession()).data.session?.access_token}`
            },
            body: JSON.stringify({
              action: actionToUse,
              priceId: planToSelect.id.weekly,
              returnUrl: window.location.origin + '/subscription/manage?success=true',
              skipTrial
            })
          });

          if (!response.ok) {
            const errorText = await response.text();
            throw new Error(`Server returned ${response.status}: ${errorText}`);
          }

          const responseData = await response.json();

          const { sessionId, error } = responseData;

          if (error) {
            throw new Error(error);
          }

          if (!sessionId) {
            throw new Error('No session ID returned from server');
          }

          // Redirect to Stripe checkout
          const stripe = await loadStripe(import.meta.env.VITE_STRIPE_PUBLISHABLE_KEY);
          if (!stripe) {
            throw new Error("Stripe failed to load");
          }

          const { error: redirectError } = await stripe.redirectToCheckout({ sessionId });
          if (redirectError) {
            throw new Error(`Stripe redirect failed: ${redirectError.message}`);
          }
        } catch (error) {
          throw error;
        }
      }
      // Current plan is paid and downgrading to Free - show message to use Manage Subscription
      else if (getCurrentPlanName() !== 'Free' && planToSelect.name === 'Free') {
        // Instead of handling cancellation here, show guidance message
        toast({
          title: "Downgrade Not Available Here",
          description: "To downgrade to the Free plan, please click the 'Manage Subscription' button and cancel your subscription through Stripe.",
          variant: "destructive",
        });
      }

    } catch (error) {
      console.error('Error selecting plan:', error);
      toast({
        title: "Plan Change Failed",
        description: `Error: ${error.message || 'Unknown error occurred'}`,
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Helper function to check if a plan is the current plan
  const isCurrentPlan = (planType: string) => {
    if (!subscription || isLoadingSubscription) {
      return planType === SUBSCRIPTION_TYPES.basic;
    }

    return subscription.subscription_type === planType;
  };

  // Helper function to check if a plan is lower than current plan
  const isLowerTier = (planType: string) => {
    if (!subscription || isLoadingSubscription) return false;

    // Check if the current plan is Pro and the selected plan is Basic
    if (subscription.subscription_type === SUBSCRIPTION_TYPES.pro && planType === SUBSCRIPTION_TYPES.basic) {
      return true;
    }

    return false;
  };

  // Calculate message usage percentage
  const messageUsagePercentage = messagesLimit > 0 ? (messagesUsed / messagesLimit) * 100 : 0;
  const currentPlanName = getCurrentPlanName();

  const shouldShowChart = () => {
    return symbol && symbolTypes?.[symbol] !== 'CRYPTO';
  };

  return (
    <div className="h-full bg-[#0A0A0A] text-white flex flex-col font-hanken-grotesk relative">
      {/* Loading Overlay */}
      {isLoading && (
        <div className="fixed inset-0 bg-black bg-opacity-70 z-50 flex items-center justify-center backdrop-blur-sm animate-fade-in">
          <div className="bg-[#141414]/90 p-8 rounded-xl border border-[#303035]/60 text-center shadow-lg backdrop-blur-md">
            <div className="flex justify-center mb-4">
              <svg className="animate-spin h-10 w-10" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
            </div>
            <h3 className="text-lg font-medium text-white mb-2">Processing Your Request</h3>
            <p className="text-white/60 text-sm">
              Please wait while we update your subscription...
            </p>
          </div>
        </div>
      )}

      <div className="flex-1 px-8 py-6 overflow-y-auto">
        <div className="flex flex-col md:flex-row md:justify-between md:items-center gap-4 md:gap-0 mb-14">
          <div>
            <h1 className="text-2xl md:text-3xl font-medium text-white mb-2" style={{ fontFamily: 'Roboto, -apple-system, BlinkMacSystemFont, sans-serif', letterSpacing: '-0.03em' }}>Subscription</h1>
            <p className="text-white/60 text-sm md:text-base" style={{ fontFamily: 'Roboto, -apple-system, BlinkMacSystemFont, sans-serif', letterSpacing: '-0.01em' }}>
              Your current plan: {currentPlanName}
            </p>
          </div>

          <div className="flex justify-start md:justify-end">
            <button
              onClick={isWhopUser
                ? () => window.location.href = 'https://whop.com/hub'
                : () => window.open('https://billing.stripe.com/p/login/fZeaGq9Iw2hAg929AA', '_blank')}
              disabled={isLoading}
              className="w-full md:w-auto px-5 py-2.5 rounded-full bg-neutral-700/40 hover:bg-neutral-600/40 text-white text-sm flex items-center justify-center gap-2 border border-[#303035]/60 disabled:opacity-50 disabled:cursor-not-allowed backdrop-blur-sm"
              style={{ fontFamily: 'Roboto, -apple-system, BlinkMacSystemFont, sans-serif', letterSpacing: '-0.01em' }}
            >
              {isLoading ? (
                <>
                  <svg className="animate-spin h-4 w-4 mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Processing...
                </>
              ) : (
                'Manage Subscription'
              )}
            </button>
          </div>
        </div>

        {/* Message Usage Card */}
        {!isWhopUser && (
          <div className="bg-[#1A1A1A]/40 rounded-xl p-6 mb-10 border border-white/[0.08] backdrop-blur-sm shadow-lg">
            <div className="flex items-center gap-3 mb-4">
              <div className="bg-neutral-700/40 p-2 rounded-full border border-neutral-600/20">
                <MessageSquare className="h-5 w-5 text-neutral-300" />
              </div>
              <h2 className="text-lg font-medium text-white" style={{ fontFamily: 'Roboto, -apple-system, BlinkMacSystemFont, sans-serif', letterSpacing: '-0.02em' }}>
                Message Usage ({currentPlanName})
              </h2>
            </div>

            {isLoadingLimits ? (
              <div className="h-20 flex items-center justify-center">
                <div className="animate-pulse flex space-x-4">
                  <div className="h-2.5 bg-gray-700 rounded w-full"></div>
                </div>
              </div>
            ) : (
              <>
                <div className="flex justify-between items-center mb-2">
                  <span className="text-white/70 text-sm">
                    {messagesUsed} of {messagesLimit} messages used
                  </span>
                  <span className="text-white/70 text-sm font-medium">
                    {messagesRemaining} remaining
                  </span>
                </div>

                <Progress
                  value={messageUsagePercentage}
                  className="h-2 bg-neutral-800"
                  indicatorClassName={
                    messageUsagePercentage > 90 ? "bg-red-500" :
                    messageUsagePercentage > 75 ? "bg-amber-500" :
                    "bg-emerald-500"
                  }
                />

                <p className="mt-4 text-white/60 text-sm" style={{ fontFamily: 'Roboto, -apple-system, BlinkMacSystemFont, sans-serif', letterSpacing: '-0.01em' }}>
                  Your message limit resets at the beginning of each billing cycle.
                  Upgrade your plan to get more messages.
                </p>
              </>
            )}
          </div>
        )}

        {/* Enhanced Horizontal Paywall Card Design */}
        <motion.div
          className="relative bg-[#141414]/40 rounded-xl w-full overflow-hidden border border-[#303035]/60 backdrop-blur-sm"
          initial={{ opacity: 0.98, scale: 0.99 }}
          animate={{
            opacity: 1,
            scale: 1,
            boxShadow: "0 0 0 1px rgba(48, 48, 53, 0.6), 0 4px 20px rgba(0, 0, 0, 0.2)"
          }}
          transition={{ duration: 0.15, ease: "easeOut" }}
        >
          {/* Header Section */}
          <div className="relative px-6 pt-5 pb-3 text-center border-b border-white/[0.06]">
            <h2 className="text-xl font-medium tracking-[-0.02em] text-white mb-1" style={{ fontFamily: 'Roboto, -apple-system, BlinkMacSystemFont, sans-serif', letterSpacing: '-0.03em' }}>
              Choose Your Plan
            </h2>
            <p className="text-white/60 text-sm max-w-[460px] mx-auto" style={{ fontFamily: 'Roboto, -apple-system, BlinkMacSystemFont, sans-serif' }}>
              Select the plan that best fits your investing needs
            </p>
          </div>

          {/* Plans Row */}
          <div className="px-5 py-4">
            <div className="grid grid-cols-1 gap-3">
              {plans.map((plan) => (
                <div
                  key={plan.id.weekly}
                  className="relative bg-[#1A1A1A]/40 rounded-lg p-4 border border-white/[0.08] hover:border-white/[0.12] transition-all duration-300 flex flex-col backdrop-blur-sm shadow-md"
                >
                  <div className="mb-3">
                    <h3 className="text-base font-medium text-white mb-1" style={{ fontFamily: 'Roboto, -apple-system, BlinkMacSystemFont, sans-serif', letterSpacing: '-0.02em' }}>{plan.name}</h3>
                    <div className="flex items-baseline mb-1">
                      <span className="text-xl font-semibold text-white" style={{ fontFamily: 'Roboto, -apple-system, BlinkMacSystemFont, sans-serif', letterSpacing: '-0.02em' }}>
                        ${plan.price.weekly}
                      </span>
                      <span className="text-white/50 ml-1.5">/week</span>
                    </div>
                    <div className="text-xs text-white/60">
                      Billed weekly, cancel anytime
                    </div>
                  </div>

                  <ul className="space-y-1.5 flex-grow">
                    {plan.features.map((feature, index) => (
                      <li key={index} className="flex items-center text-white/70 text-xs" style={{ fontFamily: 'Roboto, -apple-system, BlinkMacSystemFont, sans-serif' }}>
                        <svg className="w-3.5 h-3.5 mr-1.5 text-white/40" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                        </svg>
                        <span>{feature}</span>
                      </li>
                    ))}
                  </ul>

                  <button
                    onClick={() => handleSelectPlan(plan.type)}
                    disabled={isLoading || isCurrentPlan(plan.type) || isLowerTier(plan.type) || !subscription}
                    className={`w-full py-2 mt-3 rounded-md text-xs ${
                      isCurrentPlan(plan.type)
                        ? 'bg-neutral-700/50 text-white/80 border border-[#303035]/60 cursor-default'
                        : isLowerTier(plan.type)
                          ? 'bg-neutral-500/10 text-neutral-400 border border-neutral-500/20 cursor-not-allowed'
                          : 'bg-neutral-700/50 hover:bg-neutral-600/50 text-white font-medium border border-[#303035]/60'
                    }`}
                    style={{ fontFamily: 'Roboto, -apple-system, BlinkMacSystemFont, sans-serif' }}
                  >
                    {isLoading ? 'Processing...' : isCurrentPlan(plan.type) ? 'Current Plan' : isLowerTier(plan.type) ? 'Not Available' : 'Select'}
                  </button>
                </div>
              ))}
            </div>
          </div>

          {/* Footer */}
          <div className="px-5 pb-3 text-center">
            <div className="inline-flex items-center space-x-2">
              <svg className="w-3 h-3 text-white/40" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
              <span className="text-white/50 text-xs" style={{ fontFamily: 'Roboto, -apple-system, BlinkMacSystemFont, sans-serif' }}>Cancel anytime</span>
            </div>
          </div>
        </motion.div>

        {/* Whop User Message */}
        {isWhopUser && (
          <div className="bg-[#141414]/40 rounded-xl border border-[#303035]/60 p-6 mb-10 backdrop-blur-sm shadow-lg">
            <div className="text-center">
              <h2 className="text-xl font-medium text-white mb-4" style={{ fontFamily: 'Roboto, -apple-system, BlinkMacSystemFont, sans-serif', letterSpacing: '-0.02em' }}>Whop Subscription Active</h2>
              <p className="text-white/70 mb-6" style={{ fontFamily: 'Roboto, -apple-system, BlinkMacSystemFont, sans-serif' }}>
                You are currently subscribed through Whop. To manage your subscription, please visit the Whop Hub.
              </p>
              <button
                onClick={() => window.location.href = 'https://whop.com/hub'}
                className="px-6 py-3 bg-neutral-700/50 hover:bg-neutral-600/50 text-white rounded-lg border border-[#303035]/60"
                style={{ fontFamily: 'Roboto, -apple-system, BlinkMacSystemFont, sans-serif' }}
              >
                Go to Whop Hub
              </button>
            </div>
          </div>
        )}

        <div className="flex flex-col lg:flex-row gap-4 w-full max-w-[100vw] mx-auto px-4">
          {shouldShowChart() && (
            <div className={`w-full ${symbolTypes?.[symbol] === 'CRYPTO' ? 'lg:w-full' : 'lg:w-2/3'} h-[300px] sm:h-[400px] lg:h-[580px]`}>
              <div className="h-full w-full bg-black rounded-lg sm:rounded-xl border border-[#1A1A1C]/60 overflow-hidden shadow-lg">
                <AdvancedStockChart
                  symbol={symbol}
                  showVolume={false}
                  showControls={true}
                  theme="dark"
                  chartType="area"
                  height={300}
                  maxDataPoints={150}
                  yAxisDraggable={true}
                  defaultYAxisScale="compressed"
                  yAxisPosition="right"
                  tradingStrategy={tradingStrategy}
                />
              </div>
            </div>
          )}

          {symbolTypes?.[symbol] !== 'CRYPTO' && symbol && (
            <div className="w-full lg:w-1/3 h-[300px] sm:h-[400px] lg:h-[580px]">
              {showAIAnalysis && !loading && symbol && (
                <LoadingMessage
                  userQuery={`Analyze ${symbol}`}
                  symbols={[symbol]}
                  isComplete={true}
                  selectedResponseTypes={['expert_opinions']}
                />
              )}
              {showAIAnalysis && loading && (
                <div className="h-full w-full bg-[#0A0A0C] rounded-lg sm:rounded-xl border border-[#1A1A1C]/60 overflow-hidden shadow-lg flex items-center justify-center">
                  <div className="flex flex-col items-center">
                    <div className="h-8 w-8 border-4 border-[#1A1A1C] border-t-[#8b5cf6] rounded-full animate-spin mb-3" />
                    <div className="text-[#8b5cf6]/70 text-sm font-medium">
                      Loading expert analysis...
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Global Styles - Adding consistent styling with ChatInterface */}
      <style>
        {`
          /* Define fade-in animation */
          @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
          }

          .animate-fade-in {
            animation: fadeIn 0.15s ease-out forwards;
          }

          /* Fix for flashing focus states */
          *:focus {
            outline: none !important;
          }

          /* Remove default focus rings and overlays */
          *:focus-visible {
            outline: none !important;
            box-shadow: none !important;
          }

          /* Fix transitions to prevent flashing */
          button, input, textarea, select {
            transition: border-color 0.15s, background-color 0.15s !important;
            outline: none !important;
          }

          /* Ensure no overlay appears on focus/blur */
          button:focus, input:focus, textarea:focus, select:focus,
          button:active, input:active, textarea:active, select:active {
            box-shadow: none !important;
            outline: none !important;
            border-color: #303035 !important;
          }

          /* Fix for flashing box when clicking outside */
          div[role="dialog"]:not(:focus-within),
          button:not(:focus-within),
          input:not(:focus-within),
          select:not(:focus-within) {
            transition: none !important;
          }

          /* Fix for button hover states */
          button:hover {
            transition: background-color 0.15s ease !important;
          }

          /* Prevent any flashing on blur */
          *:not(:focus-visible) {
            transition: border-color 0.15s ease, background-color 0.15s ease !important;
          }

          /* Prevent any animations or transitions on form controls when they lose focus */
          button:not(:focus):not(:hover),
          input:not(:focus),
          textarea:not(:focus),
          select:not(:focus) {
            transition: none !important;
          }

          /* Original styles from before */
          textarea:focus-visible {
            outline: none !important;
            border-color: #303035 !important;
            box-shadow: none !important;
            ring: 0 !important;
          }

          textarea:focus {
            outline: none !important;
            border-color: #303035 !important;
            box-shadow: none !important;
            ring: 0 !important;
          }

          /* Add subtle transition effects */
          textarea, button {
            transition: all 0.15s ease;
          }

          /* Darker placeholder text on focus */
          textarea:focus::placeholder {
            color: rgba(255, 255, 255, 0.2);
          }
        `}
      </style>
    </div>
  );
}